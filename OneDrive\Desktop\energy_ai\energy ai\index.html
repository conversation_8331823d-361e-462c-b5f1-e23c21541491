<!DOCTYPE html>
<html lang="en" data-theme="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
    <meta name="description" content="Energy.AI - Smart Energy Solutions with AI-powered optimization. Reduce energy costs by 15-30% with our intelligent energy management systems. Advanced renewable energy solutions for businesses and homes in Jordan and MENA region.">
    <parameter name="keywords" content="energy AI, smart energy solutions, renewable energy, energy optimization, artificial intelligence, energy management, cost reduction, sustainability, Jordan energy, MENA energy solutions, solar power, wind energy, smart grid, IoT energy, energy analytics, carbon footprint reduction">
    <meta name="author" content="Energy.AI - Mohammad <PERSON>">
    <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1">
    <meta name="language" content="en">
    <meta name="theme-color" content="#1976d2">
    <meta name="msapplication-TileColor" content="#1976d2">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="Energy.AI">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://energy-ai.netlify.app/">
    <meta property="og:title" content="ENERGY AI">
    <meta property="og:description" content="Reduce energy costs by 15-30% with AI-powered energy optimization solutions. Smart energy management for businesses and homes. Advanced renewable energy solutions with real-time monitoring and predictive analytics.">
    <meta property="og:image" content="https://energy-ai.netlify.app/images/icon-512x512.png">
    <meta property="og:image:width" content="512">
    <meta property="og:image:height" content="512">
    <meta property="og:image:alt" content="Energy.AI Logo - Smart Energy Solutions">
    <meta property="og:site_name" content="Energy.AI">
    <meta property="og:locale" content="en_US">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://energy-ai.netlify.app/">
    <meta name="twitter:title" content="ENERGY AI">
    <meta name="twitter:description" content="Reduce energy costs by 15-30% with AI-powered energy optimization solutions. Advanced renewable energy management systems.">
    <meta name="twitter:image" content="https://energy-ai.netlify.app/images/icon-512x512.png">
    <meta name="twitter:image:alt" content="Energy.AI - Smart Energy Solutions">
    <meta name="twitter:creator" content="@EnergyAI_Jordan">
    <meta name="twitter:site" content="@EnergyAI_Jordan">

    <!-- Canonical and Alternate Languages -->
    <link rel="canonical" href="https://energy-ai.netlify.app/">
    <link rel="alternate" hreflang="en" href="https://energy-ai.netlify.app/">
    <link rel="alternate" hreflang="ar" href="https://energy-ai.netlify.app/?lang=ar">
    <link rel="alternate" hreflang="x-default" href="https://energy-ai.netlify.app/">

    <!-- PWA Manifest -->
    <link rel="manifest" href="/manifest.json">

    <!-- Favicons -->
    <link rel="icon" type="image/png" sizes="32x32" href="/images/icon-192x192.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/images/icon-192x192.png">
    <link rel="apple-touch-icon" href="/images/icon-192x192.png">

    <title>ENERGY AI</title>

    <!-- Modern Glassmorphism CSS - Inline for performance -->
    <style>
        /* Modern Glassmorphism Critical Styles */
        :root {
            --primary-color: #1976d2;
            --secondary-color: #ff7200;
            --background-primary: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            --text-primary: #ffffff;
            --text-secondary: #b0b0b0;
            --glass-bg: rgba(255, 255, 255, 0.08);
            --glass-border: rgba(255, 255, 255, 0.15);
            --transition-fast: 0.15s ease;
            --transition-normal: 0.3s ease;
        }

        [data-theme="light"] {
            --background-primary: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 25%, #ddeeff 50%, #d4e9ff 75%, #cce4ff 100%);
            --text-primary: #2c3e50;
            --text-secondary: #5d4e37;
            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.3);
        }

        body {
            margin: 0;
            padding: 0;
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: var(--background-primary);
            background-attachment: fixed;
            color: var(--text-primary);
            overflow-x: hidden;
            transition: all var(--transition-normal);
        }

        /* Glassmorphism Background */
        .glassmorphism-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            background: var(--background-primary);
        }

        /* Floating Shapes */
        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .shape {
            position: absolute;
            background: linear-gradient(135deg, rgba(255, 114, 0, 0.08), rgba(25, 118, 210, 0.08));
            border-radius: 50%;
            animation: float 25s infinite linear;
        }

        .shape:nth-child(1) { width: 120px; height: 120px; top: 15%; left: 8%; animation-delay: 0s; }
        .shape:nth-child(2) { width: 180px; height: 180px; top: 65%; left: 85%; animation-delay: 8s; }
        .shape:nth-child(3) { width: 90px; height: 90px; top: 85%; left: 15%; animation-delay: 16s; }
        .shape:nth-child(4) { width: 150px; height: 150px; top: 25%; left: 75%; animation-delay: 24s; }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg) scale(1); }
            25% { transform: translateY(-40px) rotate(90deg) scale(1.1); }
            50% { transform: translateY(-20px) rotate(180deg) scale(0.9); }
            75% { transform: translateY(-60px) rotate(270deg) scale(1.05); }
        }

        /* Glass Components */
        .glass {
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid var(--glass-border);
            border-radius: 20px;
            box-shadow:
                0 8px 32px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
        }

        .main {
            min-height: 100vh;
            position: relative;
        }

        .navbar {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            width: 90%;
            max-width: 1200px;
            padding: 15px 30px;
            transition: all var(--transition-normal);
        }

        /* Critical inline styles for immediate loading */
        .loading-screen {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--background-primary);
            display: flex;
            align-items: center;
            justify-content: flex-start;
            z-index: 9999;
            transition: opacity 0.8s ease;
            overflow: hidden;
        }

        .loading-screen.hidden {
            opacity: 0;
            pointer-events: none;
        }

        /* Energy Cards Styles (Optimization + Security + Cloud) */
        .energy-optimization-card,
        .energy-security-card,
        .cloud-energy-card {
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .energy-optimization-card:hover,
        .energy-security-card:hover,
        .cloud-energy-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(25, 118, 210, 0.3);
        }

        .card-action {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-top: 16px;
            padding-top: 16px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.3s ease;
        }

        .energy-optimization-card:hover .card-action,
        .energy-security-card:hover .card-action,
        .cloud-energy-card:hover .card-action {
            opacity: 1;
            transform: translateY(0);
        }

        .action-text {
            font-size: 14px;
            color: #1976d2;
            font-weight: 500;
        }

        .action-arrow {
            font-size: 18px;
            color: #1976d2;
            transition: transform 0.3s ease;
        }

        .energy-optimization-card:hover .action-arrow,
        .energy-security-card:hover .action-arrow,
        .cloud-energy-card:hover .action-arrow {
            transform: translateX(5px);
        }



        /* Energy Optimization Modal Styles */
        .energy-optimization-modal,
        .energy-security-modal,
        .cloud-energy-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            z-index: 10000;
            display: none;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .energy-optimization-modal.active,
        .energy-security-modal.active,
        .cloud-energy-modal.active {
            display: flex;
            opacity: 1;
        }



        .modal-container {
            display: block;
            width: 100vw;
            height: 100vh;
            background: linear-gradient(135deg, #000000 0%, #0a0a0a 10%, #1a1a2e 30%, #16213e 50%, #0f3460 70%, #533483 100%);
            position: relative;
            overflow: hidden;
        }

        .modal-sidebar {
            width: 280px;
            background: transparent;
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-right: 1px solid rgba(255, 255, 255, 0.1);
            padding: 24px;
            display: flex;
            flex-direction: column;
            position: absolute;
            top: 0;
            left: 0;
            height: 100vh;
            transform: translateX(-270px);
            transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;
            z-index: 10001;
            opacity: 0;
            box-shadow: 2px 0 20px rgba(0, 0, 0, 0.5);
        }

        .modal-sidebar::after {
            content: '';
            position: absolute;
            top: 50%;
            right: -8px;
            transform: translateY(-50%);
            width: 8px;
            height: 40px;
            background: rgba(25, 118, 210, 0.05);
            border-radius: 0 6px 6px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.4s ease;
            opacity: 0.3;
        }

        .modal-sidebar::before {
            content: '⋮';
            position: absolute;
            top: 50%;
            right: -6px;
            transform: translateY(-50%);
            color: rgba(25, 118, 210, 0.4);
            font-size: 12px;
            z-index: 1001;
            pointer-events: none;
            transition: all 0.4s ease;
            opacity: 0.5;
        }

        .modal-sidebar:hover {
            transform: translateX(0);
            opacity: 1;
        }

        .modal-sidebar:hover::after {
            background: rgba(25, 118, 210, 0.15);
            width: 12px;
            opacity: 0.8;
        }

        .modal-sidebar:hover::before {
            opacity: 0;
            transform: translateY(-50%) scale(0.5);
        }

        .sidebar-hover-area {
            position: absolute;
            top: 0;
            left: 0;
            width: 20px;
            height: 100vh;
            z-index: 10000;
            background: transparent;
        }









        /* تحسين الاستجابة للشاشات الصغيرة */
        @media (max-width: 768px) {
            .modal-sidebar {
                transform: translateX(-290px);
                opacity: 0;
                width: 90vw;
                max-width: 320px;
            }

            .sidebar-hover-area {
                width: 15px;
            }

            .modal-sidebar::after {
                right: -6px;
                width: 6px;
                height: 30px;
                opacity: 0.2;
            }

            .modal-sidebar::before {
                right: -4px;
                font-size: 10px;
                opacity: 0.3;
            }

            .modal-main-content {
                width: 100vw;
                height: 100vh;
            }

            .modal-chat-area {
                padding: 0;
                height: calc(100vh - 100px);
            }

            .chat-content-area {
                padding: 20px 15px;
                max-width: 95%;
            }

            .messages-container {
                padding: 20px 15px;
                max-width: 95%;
            }

            .message {
                max-width: 95%;
                width: 95%;
            }

            .modal-input-wrapper {
                max-width: 95%;
                width: 95%;
            }
        }

        .modal-logo {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 32px;
            padding-bottom: 24px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .modal-logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #1976d2, #ff7200);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }

        .modal-logo-text {
            font-size: 18px;
            font-weight: 600;
        }

        .modal-close-btn {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            color: #ffffff;
            font-size: 20px;
            transition: all 0.3s ease;
            z-index: 10001;
        }

        .modal-close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }

        .sidebar-close-section {
            margin-top: auto;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .sidebar-close-btn {
            width: 100%;
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 12px 16px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            justify-content: center;
            direction: rtl;
        }

        .sidebar-close-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 1);
        }

        .sidebar-close-btn svg {
            width: 14px;
            height: 14px;
        }

        .fixed-energy-title {
            position: fixed;
            top: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 10000;
            display: flex;
            align-items: center;
            gap: 12px;
            background: rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 50px;
            padding: 12px 24px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
            pointer-events: none; /* avoid intercepting clicks below */
        }

        .energy-title-icon {
            font-size: 24px;
            background: linear-gradient(135deg, #ff6b35, #f7931e);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            filter: drop-shadow(0 2px 4px rgba(255, 107, 53, 0.3));
        }

        .fixed-energy-title h1 {
            margin: 0;
            font-size: 20px;
            font-weight: 700;
            color: #ffffff;
            text-shadow: 0 2px 10px rgba(255, 255, 255, 0.3);
        }

        .new-chat-btn {
            background: linear-gradient(135deg, #1976d2, #ff7200);
            border: none;
            border-radius: 12px;
            padding: 12px 16px;
            color: white;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 24px;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .new-chat-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(25, 118, 210, 0.4);
        }

        .chat-history {
            flex: 1;
            overflow-y: auto;
        }

        .sidebar-section {
            margin-bottom: 24px;
        }

        .sidebar-section-title {
            font-size: 12px;
            font-weight: 600;
            letter-spacing: 0.08em;
            text-transform: uppercase;
            color: rgba(255, 255, 255, 0.6);
            margin: 0 0 12px;
            padding: 0 4px;
        }

        .sidebar-section:first-of-type .sidebar-section-title {
            margin-top: 0;
        }

        .chat-item {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 1px solid transparent;
        }

        .sidebar-section .chat-item {
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .chat-item-icon {
            width: 36px;
            height: 36px;
            border-radius: 12px;
            background: rgba(255, 255, 255, 0.08);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            flex-shrink: 0;
            color: #fff;
        }

        .chat-item:hover {
            background: rgba(255, 255, 255, 0.05);
            border-color: rgba(255, 255, 255, 0.1);
        }

        .chat-item.active {
            background: rgba(25, 118, 210, 0.15);
            border-color: rgba(25, 118, 210, 0.3);
        }

        .chat-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .sidebar-section .chat-title {
            white-space: normal;
        }

        .chat-preview {
            font-size: 12px;
            color: #888;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .sidebar-section .chat-preview {
            white-space: normal;
            line-height: 1.4;
            color: rgba(255, 255, 255, 0.6);
        }

        .tool-dynamic-container {
            display: none;
            flex-direction: column;
            gap: 24px;
            width: 100%;
            padding: 32px 32px 48px;
            box-sizing: border-box;
        }

        .tool-dynamic-container.active {
            display: flex;
        }

        .tool-header h2 {
            margin: 0;
            font-size: 24px;
            font-weight: 700;
            color: #ffffff;
        }

        .tool-header p {
            margin: 8px 0 0;
            color: rgba(255, 255, 255, 0.65);
            font-size: 14px;
            max-width: 640px;
        }

        .tool-stat-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .tool-stat-card {
            background: rgba(255, 255, 255, 0.06);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .tool-stat-card h3 {
            margin: 0;
            font-size: 13px;
            text-transform: uppercase;
            letter-spacing: 0.08em;
            color: rgba(255, 255, 255, 0.6);
        }

        .tool-section-title {
            margin: 0 0 12px;
            font-size: 15px;
            font-weight: 600;
            letter-spacing: 0.05em;
            color: rgba(255, 255, 255, 0.75);
        }

        .tool-stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #ffffff;
        }

        .tool-stat-trend {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
        }

        .tool-weekly-bars {
            display: grid;
            grid-template-columns: repeat(7, minmax(0, 1fr));
            gap: 12px;
            margin-top: 8px;
        }

        .tool-weekly-bar {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 12px;
            position: relative;
            height: 120px;
            overflow: hidden;
        }

        .tool-weekly-bar-fill {
            position: absolute;
            left: 0;
            bottom: 0;
            right: 0;
            background: linear-gradient(180deg, rgba(25, 118, 210, 0.8), rgba(25, 118, 210, 0.45));
            border-radius: 12px 12px 0 0;
            transition: height 0.3s ease;
        }

        .tool-weekly-bar-day {
            position: absolute;
            bottom: 6px;
            left: 0;
            width: 100%;
            text-align: center;
            font-size: 11px;
            color: rgba(255, 255, 255, 0.7);
            font-weight: 500;
        }

        .tool-form {
            display: grid;
            gap: 16px;
            max-width: 640px;
        }

        .tool-form label {
            font-size: 13px;
            color: rgba(255, 255, 255, 0.7);
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .tool-form input,
        .tool-form select,
        .tool-form textarea {
            background: rgba(10, 15, 30, 0.7);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 10px 14px;
            color: #ffffff;
            font-family: inherit;
            font-size: 14px;
            resize: vertical;
            min-height: 42px;
        }

        .tool-form input:focus,
        .tool-form select:focus,
        .tool-form textarea:focus {
            outline: none;
            border-color: rgba(25, 118, 210, 0.7);
            box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.25);
        }

        .tool-button {
            align-self: flex-start;
            padding: 12px 20px;
            border-radius: 12px;
            border: none;
            font-weight: 600;
            cursor: pointer;
            background: linear-gradient(135deg, #1976d2, #ff7200);
            color: #ffffff;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .tool-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(25, 118, 210, 0.35);
        }

        .tool-output {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: 16px;
            padding: 18px;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
        }

        .tool-output strong {
            color: #ffffff;
        }

        .tool-checklist {
            display: grid;
            gap: 12px;
            max-width: 720px;
        }

        .tool-checklist-item {
            display: flex;
            gap: 12px;
            align-items: flex-start;
            background: rgba(255, 255, 255, 0.04);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: 16px;
            padding: 14px 16px;
        }

        .tool-checklist-item input[type="checkbox"] {
            margin-top: 4px;
            width: 20px;
            height: 20px;
            accent-color: #1976d2;
        }

        .tool-progress {
            display: flex;
            flex-direction: column;
            gap: 6px;
            max-width: 360px;
        }

        .tool-progress span {
            font-size: 13px;
            color: rgba(255, 255, 255, 0.7);
        }

        .tool-progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 999px;
            overflow: hidden;
        }

        .tool-progress-fill {
            height: 100%;
            width: 0%;
            background: linear-gradient(135deg, #1976d2, #ff7200);
            transition: width 0.3s ease;
        }

        .tool-table-wrapper {
            background: rgba(255, 255, 255, 0.04);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: 16px;
            overflow: hidden;
            max-width: 900px;
        }

        .tool-table {
            width: 100%;
            border-collapse: collapse;
        }

        .tool-table th,
        .tool-table td {
            padding: 12px 16px;
            font-size: 13px;
            color: rgba(255, 255, 255, 0.85);
            border-bottom: 1px solid rgba(255, 255, 255, 0.06);
        }

        .tool-table th {
            text-transform: uppercase;
            letter-spacing: 0.06em;
            color: rgba(255, 255, 255, 0.6);
        }

        .tool-table tbody tr:hover {
            background: rgba(25, 118, 210, 0.12);
        }

        .tool-note {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.55);
        }

        .tool-report-output {
            white-space: pre-wrap;
            background: rgba(10, 15, 30, 0.7);
            border-radius: 16px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.08);
            font-size: 13px;
            line-height: 1.7;
        }

        .system-sizing-layout {
            display: flex;
            flex-wrap: wrap;
            gap: 24px;
        }

        .system-sizing-card {
            flex: 1 1 320px;
            background: rgba(255, 255, 255, 0.04);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: 20px;
            padding: 24px;
            box-sizing: border-box;
            backdrop-filter: blur(12px);
        }

        .system-sizing-mode-toggle {
            display: grid;
            grid-template-columns: repeat(2, minmax(0, 1fr));
            gap: 8px;
            margin-bottom: 16px;
        }

        .system-sizing-mode-toggle button {
            border: 1px solid rgba(255, 255, 255, 0.12);
            background: rgba(255, 255, 255, 0.05);
            color: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            padding: 12px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease, background 0.2s ease;
        }

        .system-sizing-mode-toggle button.active {
            background: linear-gradient(135deg, rgba(25, 118, 210, 0.3), rgba(255, 114, 0, 0.3));
            border-color: rgba(25, 118, 210, 0.45);
            color: #ffffff;
        }

        .system-sizing-mode-toggle button:hover {
            transform: translateY(-2px);
        }

        .system-sizing-advanced-toggle {
            margin-top: 12px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 12px 16px;
            cursor: pointer;
            color: rgba(255, 255, 255, 0.75);
            font-size: 13px;
        }

        .system-sizing-advanced-toggle:hover {
            background: rgba(255, 255, 255, 0.06);
        }

        .system-sizing-advanced {
            margin-top: 16px;
            padding-top: 12px;
            border-top: 1px solid rgba(255, 255, 255, 0.08);
            display: none;
            gap: 14px;
        }

        .system-sizing-advanced.visible {
            display: grid;
        }

        .system-sizing-card.results {
            flex: 1 1 520px;
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .system-sizing-empty {
            padding: 32px;
            text-align: center;
            background: rgba(255, 255, 255, 0.02);
            border: 1px dashed rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
        }

        .system-sizing-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 16px;
        }

        .system-sizing-summary-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: 16px;
            padding: 18px;
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .system-sizing-summary-card .label {
            font-size: 12px;
            letter-spacing: 0.06em;
            text-transform: uppercase;
            color: rgba(255, 255, 255, 0.55);
        }

        .system-sizing-summary-card .value {
            font-size: 26px;
            font-weight: 700;
            color: #ffffff;
        }

        .system-sizing-summary-card .note {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.55);
        }

        .system-sizing-section {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: 16px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .system-sizing-section h3 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.85);
        }

        .system-sizing-grid-two {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 16px;
        }

        .system-sizing-detail-card {
            background: rgba(0, 0, 0, 0.35);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: 14px;
            padding: 16px;
            display: flex;
            flex-direction: column;
            gap: 6px;
            color: rgba(255, 255, 255, 0.75);
            font-size: 13px;
        }

        .system-sizing-detail-card strong {
            font-size: 14px;
            color: #ffffff;
        }

        .system-sizing-detail-card .note {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.55);
        }

        .system-sizing-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 13px;
        }

        .system-sizing-table th,
        .system-sizing-table td {
            padding: 10px 12px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
        }

        .system-sizing-table th {
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: rgba(255, 255, 255, 0.6);
            text-align: right;
        }

        .system-sizing-table td {
            color: rgba(255, 255, 255, 0.75);
            text-align: right;
        }

        .system-sizing-recommendations {
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: 16px;
            padding: 20px;
            font-size: 13px;
            color: rgba(255, 255, 255, 0.75);
            line-height: 1.7;
        }

        .system-sizing-recommendations ul {
            margin: 12px 0 0;
            padding: 0 18px;
        }

        .system-sizing-recommendations li {
            margin-bottom: 6px;
        }

        .system-sizing-pill {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 6px 10px;
            border-radius: 999px;
            background: rgba(25, 118, 210, 0.15);
            color: #9cc9ff;
            font-size: 12px;
            font-weight: 600;
            letter-spacing: 0.05em;
            text-transform: uppercase;
        }

        .system-sizing-mode-field.hidden,
        .hidden {
            display: none !important;
        }

        @media (max-width: 768px) {
            .system-sizing-card {
                padding: 20px;
            }

            .system-sizing-summary-card .value {
                font-size: 22px;
            }
        }
        .live-sim-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 24px;
        }

        .live-sim-settings,
        .live-sim-visual {
            background: rgba(255, 255, 255, 0.04);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: 20px;
            padding: 24px;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            gap: 20px;
            min-width: 300px;
        }

        .live-sim-settings {
            flex: 1 1 340px;
            max-width: 400px;
            backdrop-filter: blur(12px);
        }

        .live-sim-visual {
            flex: 1 1 540px;
        }

        .live-sim-title {
            margin: 0;
            font-size: 20px;
            font-weight: 700;
            color: #ffffff;
        }

        .live-sim-subtitle {
            margin: 4px 0 0;
            color: rgba(255, 255, 255, 0.65);
            font-size: 13px;
        }

        .live-sim-map {
            width: 100%;
            height: 260px;
            border-radius: 16px;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.08);
            box-shadow: inset 0 0 0 1px rgba(0, 0, 0, 0.1);
        }

        .live-sim-coordinates {
            display: grid;
            grid-template-columns: repeat(2, minmax(0, 1fr));
            gap: 12px;
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }

        .live-sim-coordinates span {
            display: block;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 10px;
            padding: 8px 10px;
            border: 1px solid rgba(255, 255, 255, 0.08);
        }

        .live-sim-form {
            display: grid;
            gap: 14px;
        }

        .live-sim-form label {
            display: flex;
            flex-direction: column;
            gap: 6px;
            font-size: 13px;
            color: rgba(255, 255, 255, 0.75);
        }

        .live-sim-form input {
            background: rgba(10, 15, 30, 0.7);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: 12px;
            padding: 10px 12px;
            color: #ffffff;
            font-size: 14px;
        }

        .live-sim-form input:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .live-sim-status {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.65);
            background: rgba(25, 118, 210, 0.12);
            border: 1px solid rgba(25, 118, 210, 0.25);
            border-radius: 12px;
            padding: 10px 12px;
            line-height: 1.5;
        }

        .live-sim-stats,
        .live-sim-weather,
        .live-sim-summary {
            display: grid;
            gap: 16px;
        }

        .live-sim-stats {
            grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
        }

        .live-sim-weather {
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
        }

        .live-sim-stat-card,
        .live-sim-weather-card,
        .live-sim-summary-card {
            background: rgba(255, 255, 255, 0.04);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: 16px;
            padding: 16px;
            display: flex;
            flex-direction: column;
            gap: 6px;
            text-align: center;
        }

        .live-sim-stat-value {
            font-size: 28px;
            font-weight: 700;
            color: #ffffff;
        }

        .live-sim-stat-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
        }

        .live-sim-analysis {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 16px;
            font-size: 13px;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
        }

        .live-sim-charts {
            display: grid;
            gap: 24px;
        }

        .live-sim-chart-card {
            background: rgba(0, 0, 0, 0.35);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: 18px;
            padding: 16px;
        }

        .live-sim-chart-card h3 {
            margin: 0 0 12px;
            font-size: 15px;
            font-weight: 600;
            color: rgba(255, 255, 255, 0.85);
        }

        .live-sim-chart-card canvas {
            width: 100%;
            display: block;
            height: 240px !important;
        }

        .live-sim-summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
            gap: 16px;
        }

        .live-sim-error {
            background: rgba(255, 82, 82, 0.18);
            border-color: rgba(255, 82, 82, 0.4);
        }

        @media (max-width: 1024px) {
            .live-sim-grid {
                flex-direction: column;
            }

            .live-sim-settings,
            .live-sim-visual {
                max-width: 100%;
            }
        }

        @media (max-width: 768px) {
            .live-sim-settings,
            .live-sim-visual {
                padding: 20px;
            }

            .live-sim-map {
                height: 220px;
            }
        }
        @media (max-width: 768px) {
            .tool-dynamic-container {
                padding: 24px 20px 120px;
            }

            .tool-stat-grid {
                grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
            }

            .tool-weekly-bar {
                height: 100px;
            }
        }

        .modal-main-content {
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
            background: transparent;
            position: relative;
        }

        .modal-header {
            padding: 80px 32px 24px;
            border-bottom: none;
            background: transparent;
        }

        .modal-header h1 {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            color: white;
        }

        .modal-header p {
            color: #888;
            font-size: 14px;
        }

        .modal-chat-area {
            flex: 1;
            padding: 0;
            overflow-y: auto;
            scroll-behavior: smooth;
            display: flex;
            flex-direction: column;
            position: relative;
            width: 100%;
            height: calc(100vh - 120px);
            align-items: center;
            justify-content: center;
        }

        /* Add safe space under the fixed pill title inside modals */
        .energy-optimization-modal .modal-chat-area,
        .energy-security-modal .modal-chat-area,
        .cloud-energy-modal .modal-chat-area {
            padding-top: 110px; /* prevent Welcome/input from sitting under the title */
        }

        /* Reserve space for the fixed bottom input bar so messages do not hide underneath */
        .modal-chat-area { padding-bottom: 220px; }

        .chat-content-area {
            text-align: center;
            margin: auto;
            max-width: 800px;
            width: 90%;
            padding: 40px 20px;
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            overflow: visible; /* avoid nested scroll; let modal-chat-area scroll */
            min-height: 60vh;
        }

        /* Ensure messages never hide behind the bottom input bar */
        .chat-content-area,
        .messages-container,
        .messages-list {
            padding-bottom: 180px; /* space for fixed input bar */
        }

        .welcome-content {
            display: flex !important;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            position: relative;
            visibility: visible !important;
            opacity: 1 !important;
            z-index: 10;
        }

        .welcome-icon {
            display: none;
        }

        .welcome-title {
            display: block !important;
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 32px;
            text-align: center;
            color: #ffffff;
            text-shadow: 0 4px 20px rgba(255, 255, 255, 0.4);
            visibility: visible !important;
            opacity: 1 !important;
        }

        /* Force compact welcome title inside modals (avoid hero overrides) */
        .energy-optimization-modal .welcome-title,
        .energy-security-modal .welcome-title {
            font-size: 36px !important;
            margin-bottom: 32px !important;
            text-align: center !important;
            text-shadow: 0 4px 20px rgba(255, 255, 255, 0.4) !important;
        }

        .welcome-subtitle {
            color: #888;
            font-size: 16px;
            margin-bottom: 32px;
            line-height: 1.5;
        }

        /* Dedicated modal welcome title to avoid conflicts with page hero */
        .modal-welcome-title {
            display: block !important;
            font-size: 36px !important;
            font-weight: 700 !important;
            margin-bottom: 32px !important;
            text-align: center !important;
            color: #ffffff !important;
            text-shadow: 0 4px 20px rgba(255, 255, 255, 0.4) !important;
            visibility: visible !important;
            opacity: 1 !important;
        }



        .messages-container {
            display: none;
            flex-direction: column;
            gap: 32px;
            padding: 40px 20px;
            overflow: visible; /* avoid nested scroll; let modal-chat-area scroll */
            width: 100%;
            max-width: 900px;
            margin: 0 auto;
            flex: 1;
            justify-content: flex-start;
            align-items: center;
        }

        .message {
            display: flex;
            gap: 0px;
            max-width: 800px;
            width: 90%;
            margin: 0 auto; /* remove vertical space between message and reply */
            align-items: center;
            justify-content: center;
        }

        .message.user {
            align-self: flex-end;
            flex-direction: row-reverse;
        }

        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: none;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            flex-shrink: 0;
        }

        .message.ai .message-avatar {
            background: linear-gradient(135deg, #1976d2, #ff7200);
        }

        .message.user .message-avatar {
            background: rgba(255, 255, 255, 0.1);
        }

        .message-content {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.12);
            border-radius: 20px;
            padding: 24px 32px;
            line-height: 1.7;
            color: white;
            font-size: 15px;
            width: 100%;
            max-width: 700px;
            word-wrap: break-word;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            text-align: center;
            margin: 0 auto;
        }

        .message.user .message-content {
            background: rgba(25, 118, 210, 0.1);
            border-color: rgba(25, 118, 210, 0.2);
        }

        .modal-input-container {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
        }

        .modal-input-container.center-input {
            padding: 40px 20px;
            margin: 40px 0;
            position: relative;
        }

        .modal-input-container.bottom-input {
            padding: 24px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(255, 255, 255, 0.02);
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }

        .modal-input-wrapper {
            position: relative;
            display: flex;
            align-items: center;
            background: rgba(20, 20, 20, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.15);
            border-radius: 30px;
            padding: 12px;
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
            width: 100%;
            max-width: 700px;
            transition: all 0.3s ease;
        }

        .modal-input-field {
            flex: 1;
            background: transparent;
            border: none;
            resize: none;
            padding: 12px 16px;
            color: #ffffff;
            font-size: 15px;
            line-height: 1.4;
            outline: none;
            font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
            min-height: 20px;
            max-height: 120px;
            overflow-y: auto;
            direction: rtl;
            text-align: center;
        }

        .modal-input-field::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .modal-action-btn {
            background: transparent;
            border: none;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: rgba(255, 255, 255, 0.6);
            margin: 0 4px;
        }

        .modal-action-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.9);
        }

        .modal-send-btn {
            background: rgba(25, 118, 210, 0.3);
            border: none;
            border-radius: 50%;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: rgba(255, 255, 255, 0.9);
            margin-left: 4px;
        }

        .modal-send-btn:hover {
            background: rgba(25, 118, 210, 0.5);
            transform: scale(1.05);
        }

        .modal-action-btn {
            background: transparent;
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: rgba(255, 255, 255, 0.6);
        }

        .modal-action-btn:hover {
            background: rgba(255, 255, 255, 0.1);
            color: rgba(255, 255, 255, 0.9);
        }

        .modal-send-btn {
            position: static;
            transform: none;
        }

        .modal-send-btn:hover {
            background: rgba(25, 118, 210, 0.3);
            color: rgba(255, 255, 255, 1);
            transform: scale(1.05);
        }


        @media (max-width: 768px) {
            .modal-container {
                flex-direction: column;
            }

            .modal-sidebar {
                width: 100%;
                height: auto;
                border-right: none;
                border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            }


        }
    </style>

    <!-- Critical CSS - Load immediately -->
    <link rel="stylesheet" href="css/glassmorphism.css">

    <link rel="stylesheet" href="css/welcome-screen.css">

    <!-- CSS Files - Load non-critical CSS asynchronously -->
    <link rel="preload" href="css/performance-optimizations.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="css/styles.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="preload" href="css/space-background.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <link rel="stylesheet" href="css/icons.css">
    <link rel="stylesheet" href="css/auth.css">
    <link rel="stylesheet" href="css/naya.css">
    <link rel="stylesheet" href="css/enhanced-map-animations.css">
    <link rel="stylesheet" href="css/neon-cursor.css">
    <link rel="stylesheet" href="css/advanced-system-calculator.css">

    <!-- Fallback for browsers that don't support preload -->
    <noscript>
        <link rel="stylesheet" href="css/performance-optimizations.css">
        <link rel="stylesheet" href="css/styles.css">
        <link rel="stylesheet" href="css/space-background.css">
        <link rel="stylesheet" href="css/welcome-screen.css">
    </noscript>


    <!-- Leaflet CSS -->
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossorigin=""/>

    <!-- Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Glassmorphism Background -->
    <div class="glassmorphism-bg"></div>

    <!-- Floating Shapes -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <!-- Welcome Screen -->
    <div class="loading-screen" id="loadingScreen">
        <div class="welcome-container">
            <div class="welcome-logo-section">
                <div class="welcome-logo">
                    <div class="site-logo large-logo"></div>
                    <div class="logo-glow"></div>
                </div>
                <h1 class="welcome-title">Welcome to</h1>
                <h2 class="brand-name">Energy.AI</h2>
                <p class="welcome-subtitle">Smart Energy Solutions Powered by AI</p>
            </div>
            <div class="welcome-animation">
                <div class="energy-particles"></div>
                <div class="loading-progress">
                    <div class="progress-bar"></div>
                </div>
                <button class="skip-loading-btn" onclick="skipLoading()" style="
                    position: absolute;
                    bottom: 30px;
                    right: 30px;
                    background: rgba(255, 255, 255, 0.1);
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    color: white;
                    padding: 10px 20px;
                    border-radius: 25px;
                    cursor: pointer;
                    font-size: 14px;
                    transition: all 0.3s ease;
                    backdrop-filter: blur(10px);
                " onmouseover="this.style.background='rgba(255, 255, 255, 0.2)'" onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'">
                    Skip ⏭️
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="main">
        <nav class="navbar glass">
            <div class="nav-content">
                <a href="#home" class="nav-logo">
                    <div class="nav-logo-icon">⚡</div>
                    <span>Energy.AI</span>
                </a>
                <ul class="nav-menu" id="navMenu">
                    <li><a href="#home" class="nav-link active">Home</a></li>
                    <li><a href="#about" class="nav-link">About</a></li>
                    <li><a href="#service" class="nav-link">Services</a></li>
                    <li><a href="#design" class="nav-link">Design</a></li>
                    <li><a href="#contact" class="nav-link">Contact</a></li>
                </ul>
                <div class="nav-actions">
                    <button class="mobile-menu-toggle" id="mobileMenuToggle">
                        <ion-icon name="menu-outline"></ion-icon>
                    </button>
                </div>
            </div>
        </nav>
        <section class="hero" id="home">
            <div class="hero-content">
                <h1 class="hero-title">
                    <span class="title-line-1">Web Design &</span>
                    <span class="title-line-2">Development</span>
                    <span class="title-line-3">Energy</span>
                </h1>
                <p class="hero-description">
                    AI is the spark igniting a new era of energy innovation<br>
                    powering tomorrow with<br>
                    intelligent solutions today
                </p>
                <div class="hero-actions">
                    <button class="btn btn-primary" id="joinBtn">
                        <ion-icon name="person-add-outline"></ion-icon>
                        Join Us
                    </button>
                    <button class="btn btn-secondary">Book Free Consultation</button>
                </div>
            </div>

            <!-- Floating Glass Elements -->
            <div class="floating-glass floating-glass-1"></div>
            <div class="floating-glass floating-glass-2"></div>
            <div class="floating-glass floating-glass-3"></div>
        </section>

        <!-- Auth Modal will be created by auth-system.js -->

        <!-- Fixed CTA Button -->
        <div class="fixed-cta-container">
            <button class="fixed-cta-btn" id="fixedCtaBtn">
                <ion-icon name="call-outline"></ion-icon>
                <span>Get Free Consultation</span>
            </button>
        </div>

        <!-- AI Chat Interface -->
        <div class="ai-chat-container" id="aiChatContainer">
            <div class="chat-header">
                <h3>Energy.AI Assistant</h3>
                <div class="chat-controls">
                    <button class="minimize-btn chat-control-btn" id="minimizeChatBtn">
                        <ion-icon name="remove-outline"></ion-icon>
                    </button>
                    <button class="close-btn chat-control-btn" id="closeChatBtn">
                        <ion-icon name="close-outline"></ion-icon>
                    </button>
                </div>
            </div>
            <div class="chat-messages" id="chatMessages">
                <div class="message bot">
                    <div class="avatar">
                        <div class="avatar-icon-wrapper">
                            <ion-icon name="analytics-outline"></ion-icon>
                        </div>
                    </div>
                    <div class="message-content">Hello, I'm Energy.AI Assistant</div>
                </div>
            </div>
            <div class="suggested-questions">
                <button class="question-btn">How can AI improve energy efficiency?</button>
                <button class="question-btn">What is renewable energy?</button>
                <button class="question-btn">How to reduce electricity bills?</button>
            </div>
            <div class="chat-input">
                <input type="text" id="userInput" placeholder="Type your message here..." aria-label="Message input">
                <button id="sendAttachmentBtn" class="chat-icon-btn" aria-label="Send attachment">
                    <ion-icon name="attach-outline"></ion-icon>
                </button>
                <button id="sendMessageBtn" class="chat-send-btn">
                    <ion-icon name="send-outline"></ion-icon>
                    <span>Send</span>
                </button>
            </div>
            <div class="typing-indicator" id="typingIndicator">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>

        <section id="about" class="section">
            <h2>About Energy.AI</h2>
            <div class="cards-grid">
                <div class="glass-card">
                    <div class="card-icon">⚡</div>
                    <h3>Smart Energy Management</h3>
                    <p>AI-powered solutions to optimize energy consumption in real-time.</p>
                </div>
                <div class="glass-card">
                    <div class="card-icon">🌱</div>
                    <h3>Sustainable Solutions</h3>
                    <p>Eco-friendly approaches to energy production and distribution.</p>
                </div>
                <div class="glass-card">
                    <div class="card-icon">📊</div>
                    <h3>Data-Driven Insights</h3>
                    <p>Comprehensive analytics to make informed energy decisions.</p>
                </div>
            </div>
        </section>

        <section id="service" class="section">
            <h2>Our Services</h2>
            <div class="cards-grid">
                <div class="glass-card energy-optimization-card" onclick="openEnergyOptimization()">
                    <div class="card-icon">💡</div>
                    <h3>Optimizing PV with AI</h3>
                    <p>Our AI algorithms analyze your energy consumption patterns and suggest optimizations to reduce waste and cost.</p>
                    <div class="card-action">
                        <span class="action-text">Click to start AI consultation</span>
                        <span class="action-arrow">→</span>
                    </div>
                </div>

                <div class="glass-card energy-security-card" onclick="openEnergySecurity()">
                    <div class="card-icon">🛡️</div>
                    <h3>Optimizing Wind Energy with AI</h3>
                    <p>Protect your energy infrastructure with advanced threat detection and response systems.</p>
                    <div class="card-action">
                        <span class="action-text">Click to start AI security</span>
                        <span class="action-arrow">→</span>
                    </div>
                </div>
                <div class="glass-card cloud-energy-card" onclick="openCloudEnergy()">
                    <div class="card-icon">☁️</div>
                    <h3>Cloud Energy Management</h3>
                    <p>Access your energy data and controls from anywhere with our secure cloud-based platform.</p>
                    <div class="card-action">
                        <span class="action-text">Click to start AI cloud</span>
                        <span class="action-arrow">→</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Energy Optimization Modal -->
        <div class="energy-optimization-modal" id="energyOptimizationModal">
            <div class="modal-container">
                <div class="sidebar-hover-area"></div>

                <div class="modal-sidebar">
                    <div class="modal-logo">
                        <div class="modal-logo-icon">⚡</div>
                        <div class="modal-logo-text">Optimizing PV with AI</div>
                    </div>

                    <div class="chat-history">
                        <div class="sidebar-section">
                            <div class="sidebar-section-title">لوحة المنصة</div>
                            <div class="chat-item active" data-tool="dashboard">
                                <div class="chat-item-icon">📊</div>
                                <div>
                                    <div class="chat-title">لوحة التحكم</div>
                                    <div class="chat-preview">نظرة شاملة على أداء النظام ومؤشرات الطاقة</div>
                                </div>
                            </div>
                        </div>

                        <div class="sidebar-section">
                            <div class="sidebar-section-title">AI Tools</div>
                            <div class="chat-item" data-tool="live-simulation">
                                <div class="chat-item-icon">🌤️</div>
                                <div>
                                    <div class="chat-title">المحاكاة الحية</div>
                                    <div class="chat-preview">تشغيل سيناريوهات الأداء بالاعتماد على بيانات الطقس لحظيًا</div>
                                </div>
                            </div>
                            <div class="chat-item" data-tool="system-sizing">
                                <div class="chat-item-icon">🧮</div>
                                <div>
                                    <div class="chat-title">حاسبة حجم النظام</div>
                                    <div class="chat-preview">تحديد أفضل مزيج للألواح والعاكسات لكل مشروع</div>
                                </div>
                            </div>
                            <div class="chat-item" data-tool="field-inspector">
                                <div class="chat-item-icon">🛠️</div>
                                <div>
                                    <div class="chat-title">المفتش الميداني</div>
                                    <div class="chat-preview">قوائم فحص ذكية لمرافقة فرق الموقع أثناء التنفيذ</div>
                                </div>
                            </div>
                        </div>

                        <div class="sidebar-section">
                            <div class="sidebar-section-title">Calculators</div>
                            <div class="chat-item" data-tool="string-config">
                                <div class="chat-item-icon">🔗</div>
                                <div>
                                    <div class="chat-title">تهيئة السلاسل</div>
                                    <div class="chat-preview">تحسين ترتيب الألواح حسب حدود الجهد والتيار</div>
                                </div>
                            </div>
                            <div class="chat-item" data-tool="area-calculator">
                                <div class="chat-item-icon">📐</div>
                                <div>
                                    <div class="chat-title">حاسبة المساحة</div>
                                    <div class="chat-preview">قياس المساحات المتاحة لتركيب النظام الشمسي</div>
                                </div>
                            </div>
                            <div class="chat-item" data-tool="battery-calculator">
                                <div class="chat-item-icon">🔋</div>
                                <div>
                                    <div class="chat-title">حاسبة البطاريات</div>
                                    <div class="chat-preview">التنبؤ بسعة التخزين المطلوبة وطول دورة الحياة</div>
                                </div>
                            </div>
                            <div class="chat-item" data-tool="wire-sizing">
                                <div class="chat-item-icon">⚙️</div>
                                <div>
                                    <div class="chat-title">حجم الأسلاك</div>
                                    <div class="chat-preview">اختيار سماكة الأسلاك وفق التيار والمسافة بأمان</div>
                                </div>
                            </div>
                        </div>

                        <div class="sidebar-section">
                            <div class="sidebar-section-title">Data &amp; Reports</div>
                            <div class="chat-item" data-tool="pricing-data">
                                <div class="chat-item-icon">💲</div>
                                <div>
                                    <div class="chat-title">بيانات التسعير</div>
                                    <div class="chat-preview">أسعار المكونات المحدثة وتحليل الجدوى المالية</div>
                                </div>
                            </div>
                            <div class="chat-item" data-tool="report-builder">
                                <div class="chat-item-icon">📑</div>
                                <div>
                                    <div class="chat-title">التقرير</div>
                                    <div class="chat-preview">إنشاء تقارير تفصيلية جاهزة للمشاركة مع العملاء</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Close button in sidebar -->
                    <div class="sidebar-close-section">
                        <button class="sidebar-close-btn" onclick="closeEnergyOptimization()">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            </svg>
                            إغلاق المحادثة
                        </button>
                    </div>
                </div>

                <div class="modal-main-content">
                    <!-- Fixed Energy AI Title -->
                    <div class="fixed-energy-title">
                        <div class="energy-title-icon">⚡</div>
                        <h1>Energy AI</h1>
                    </div>

                    <div class="modal-header">
                    </div>

                    <div class="modal-chat-area">
                        <div class="chat-content-area" id="chatContentArea">
                            <!-- Welcome content -->
                            <div class="welcome-content" id="welcomeContent">
                                <h2 class="modal-welcome-title">Welcome</h2>



                                <!-- Input in center when welcome is shown -->
                                <div class="modal-input-container center-input" id="centerInputContainer">
                                    <div class="modal-input-wrapper">
                                        <button class="modal-action-btn" title="إرفاق ملف">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M21.44 11.05L12.25 20.24C11.12 21.37 9.31 21.37 8.18 20.24C7.05 19.11 7.05 17.3 8.18 16.17L15.84 8.51C16.97 7.38 18.78 7.38 19.91 8.51C21.04 9.64 21.04 11.45 19.91 12.58L13.76 18.73C13.2 19.29 12.31 19.29 11.75 18.73C11.19 18.17 11.19 17.28 11.75 16.72L17.9 10.57" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </button>
                                        <button class="modal-action-btn" title="تسجيل صوتي">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
                                                <path d="M19 10V12C19 16.42 15.42 20 11 20H13C17.42 20 21 16.42 21 12V10H19Z" fill="currentColor"/>
                                                <path d="M5 10V12C5 16.42 8.58 20 13 20H11C6.58 20 3 16.42 3 12V10H5Z" fill="currentColor"/>
                                                <path d="M12 20V23M8 23H16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                            </svg>
                                        </button>
                                        <textarea
                                            class="modal-input-field"
                                            id="modalMessageInput"
                                            placeholder="اسأل عن أي شيء..."
                                            rows="1"
                                        ></textarea>
                                        <button class="modal-send-btn" onclick="sendModalMessage()" title="إرسال">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="tool-dynamic-container" id="toolDynamicContainer"></div>

                            <!-- Messages will be added directly here -->
                        </div>
                    </div>

                    <!-- Input at bottom when chatting -->
                    <div class="modal-input-container bottom-input" id="bottomInputContainer" style="display: none;">
                        <div class="modal-input-wrapper">
                            <button class="modal-action-btn" title="إرفاق ملف">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M21.44 11.05L12.25 20.24C11.12 21.37 9.31 21.37 8.18 20.24C7.05 19.11 7.05 17.3 8.18 16.17L15.84 8.51C16.97 7.38 18.78 7.38 19.91 8.51C21.04 9.64 21.04 11.45 19.91 12.58L13.76 18.73C13.2 19.29 12.31 19.29 11.75 18.73C11.19 18.17 11.19 17.28 11.75 16.72L17.9 10.57" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                            <button class="modal-action-btn" title="تسجيل صوتي">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
                                    <path d="M19 10V12C19 16.42 15.42 20 11 20H13C17.42 20 21 16.42 21 12V10H19Z" fill="currentColor"/>
                                    <path d="M5 10V12C5 16.42 8.58 20 13 20H11C6.58 20 3 16.42 3 12V10H5Z" fill="currentColor"/>
                                    <path d="M12 20V23M8 23H16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                </svg>
                            </button>
                            <textarea
                                class="modal-input-field"
                                id="modalMessageInputBottom"
                                placeholder="اسأل عن أي شيء..."
                                rows="1"
                            ></textarea>
                            <button class="modal-send-btn" onclick="sendModalMessageFromBottom()" title="إرسال">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
                                </svg>
                            </button>
                        </div>
                    </div>


                </div>
            </div>
        </div>



        <!-- Energy Security Modal (independent copy) -->
        <div class="energy-security-modal" id="energySecurityModal">
            <div class="modal-container">
                <div class="sidebar-hover-area"></div>

                <div class="modal-sidebar">
                    <div class="modal-logo">
                        <div class="modal-logo-icon">🛡️</div>
                        <div class="modal-logo-text">Optimizing Wind Energy with AI</div>
                    </div>

                    <button class="new-chat-btn" onclick="startNewChatSecurity()">
                        <span>+</span>
                        New Chat
                    </button>

                    <div class="chat-history">
                        <div class="chat-item active">
                            <div class="chat-title">Optimizing Wind Energy with AI</div>
                            <div class="chat-preview">Threat detection and response</div>
                        </div>
                        <div class="chat-item">
                            <div class="chat-title">Grid Protection</div>
                            <div class="chat-preview">Best practices for resilience</div>
                        </div>
                        <div class="chat-item">
                            <div class="chat-title">Device Hardening</div>
                            <div class="chat-preview">Secure IoT energy devices</div>
                        </div>
                    </div>

                    <div class="sidebar-close-section">
                        <button class="sidebar-close-btn" onclick="closeEnergySecurity()">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            </svg>
                            إغلاق المحادثة
                        </button>
                    </div>
                </div>

                <div class="modal-main-content">
                    <div class="fixed-energy-title">
                        <div class="energy-title-icon">🛡️</div>
                        <h1>Energy.AI</h1>
                    </div>

                    <div class="modal-chat-area">
                        <div class="chat-content-area" id="chatContentAreaSec">
                            <div class="welcome-content" id="welcomeContentSec">
                                <h2 class="modal-welcome-title">Welcome</h2>

                                <div class="modal-input-container center-input" id="centerInputContainerSec">
                                    <div class="modal-input-wrapper">
                                        <button class="modal-action-btn" title="إرفاق ملف">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M21.44 11.05L12.25 20.24C11.12 21.37 9.31 21.37 8.18 20.24C7.05 19.11 7.05 17.3 8.18 16.17L15.84 8.51C16.97 7.38 18.78 7.38 19.91 8.51C21.04 9.64 21.04 11.45 19.91 12.58L13.76 18.73C13.2 19.29 12.31 19.29 11.75 18.73C11.19 18.17 11.19 17.28 11.75 16.72L17.9 10.57" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </button>
                                        <button class="modal-action-btn" title="تسجيل صوتي">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
                                                <path d="M19 10V12C19 16.42 15.42 20 11 20H13C17.42 20 21 16.42 21 12V10H19Z" fill="currentColor"/>
                                                <path d="M5 10V12C5 16.42 8.58 20 13 20H11C6.58 20 3 16.42 3 12V10H5Z" fill="currentColor"/>
                                                <path d="M12 20V23M8 23H16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                            </svg>
                                        </button>
                                        <textarea class="modal-input-field" id="modalMessageInputSec" placeholder="اسأل عن أي شيء..." rows="1"></textarea>
                                        <button class="modal-send-btn" onclick="sendModalMessageSec()" title="إرسال">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-input-container bottom-input" id="bottomInputContainerSec" style="display: none;">
                        <div class="modal-input-wrapper">
                            <button class="modal-action-btn" title="إرفاق ملف">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M21.44 11.05L12.25 20.24C11.12 21.37 9.31 21.37 8.18 20.24C7.05 19.11 7.05 17.3 8.18 16.17L15.84 8.51C16.97 7.38 18.78 7.38 19.91 8.51C21.04 9.64 21.04 11.45 19.91 12.58L13.76 18.73C13.2 19.29 12.31 19.29 11.75 18.73C11.19 18.17 11.19 17.28 11.75 16.72L17.9 10.57" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                            <button class="modal-action-btn" title="تسجيل صوتي">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
                                    <path d="M19 10V12C19 16.42 15.42 20 11 20H13C17.42 20 21 16.42 21 12V10H19Z" fill="currentColor"/>
                                    <path d="M5 10V12C5 16.42 8.58 20 13 20H11C6.58 20 3 16.42 3 12V10H5Z" fill="currentColor"/>
                                    <path d="M12 20V23M8 23H16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                </svg>
                            </button>
                            <textarea class="modal-input-field" id="modalMessageInputBottomSec" placeholder="اسأل عن أي شيء..." rows="1"></textarea>
                            <button class="modal-send-btn" onclick="sendModalMessageFromBottomSec()" title="إرسال">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <!-- Cloud Energy Management Modal (independent copy) -->
        <div class="cloud-energy-modal" id="cloudEnergyModal">
            <div class="modal-container">
                <div class="sidebar-hover-area"></div>

                <div class="modal-sidebar">
                    <div class="modal-logo">
                        <div class="modal-logo-icon">☁️</div>
                        <div class="modal-logo-text">Cloud Energy Management</div>
                    </div>

                    <button class="new-chat-btn" onclick="startNewChatCloud()">
                        <span>+</span>
                        New Chat
                    </button>

                    <div class="chat-history">
                        <div class="chat-item active">
                            <div class="chat-title">Cloud Energy Management</div>
                            <div class="chat-preview">Manage and monitor in the cloud</div>
                        </div>
                        <div class="chat-item">
                            <div class="chat-title">Realtime Dashboards</div>
                            <div class="chat-preview">Live energy KPIs</div>
                        </div>
                        <div class="chat-item">
                            <div class="chat-title">Data Pipelines</div>
                            <div class="chat-preview">Ingest, store, and analyze</div>
                        </div>
                    </div>

                    <div class="sidebar-close-section">
                        <button class="sidebar-close-btn" onclick="closeCloudEnergy()">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            </svg>
                            إغلاق المحادثة
                        </button>
                    </div>
                </div>

                <div class="modal-main-content">
                    <div class="fixed-energy-title">
                        <div class="energy-title-icon">☁️</div>
                        <h1>Energy AI</h1>
                    </div>

                    <div class="modal-chat-area">
                        <div class="chat-content-area" id="chatContentAreaCloud">
                            <div class="welcome-content" id="welcomeContentCloud">
                                <h2 class="modal-welcome-title">Welcome</h2>

                                <div class="modal-input-container center-input" id="centerInputContainerCloud">
                                    <div class="modal-input-wrapper">
                                        <button class="modal-action-btn" title="إرفاق ملف">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M21.44 11.05L12.25 20.24C11.12 21.37 9.31 21.37 8.18 20.24C7.05 19.11 7.05 17.3 8.18 16.17L15.84 8.51C16.97 7.38 18.78 7.38 19.91 8.51C21.04 9.64 21.04 11.45 19.91 12.58L13.76 18.73C13.2 19.29 12.31 19.29 11.75 18.73C11.19 18.17 11.19 17.28 11.75 16.72L17.9 10.57" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </button>
                                        <button class="modal-action-btn" title="تسجيل صوتي">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
                                                <path d="M19 10V12C19 16.42 15.42 20 11 20H13C17.42 20 21 16.42 21 12V10H19Z" fill="currentColor"/>
                                                <path d="M5 10V12C5 16.42 8.58 20 13 20H11C6.58 20 3 16.42 3 12V10H5Z" fill="currentColor"/>
                                                <path d="M12 20V23M8 23H16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                            </svg>
                                        </button>
                                        <textarea class="modal-input-field" id="modalMessageInputCloud" placeholder="اسأل عن أي شيء..." rows="1"></textarea>
                                        <button class="modal-send-btn" onclick="sendModalMessageCloud()" title="إرسال">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                                <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="modal-input-container bottom-input" id="bottomInputContainerCloud" style="display: none;">
                        <div class="modal-input-wrapper">
                            <button class="modal-action-btn" title="إرفاق ملف">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M21.44 11.05L12.25 20.24C11.12 21.37 9.31 21.37 8.18 20.24C7.05 19.11 7.05 17.3 8.18 16.17L15.84 8.51C16.97 7.38 18.78 7.38 19.91 8.51C21.04 9.64 21.04 11.45 19.91 12.58L13.76 18.73C13.2 19.29 12.31 19.29 11.75 18.73C11.19 18.17 11.19 17.28 11.75 16.72L17.9 10.57" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </button>
                            <button class="modal-action-btn" title="تسجيل صوتي">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M12 1C10.34 1 9 2.34 9 4V12C9 13.66 10.34 15 12 15C13.66 15 15 13.66 15 12V4C15 2.34 13.66 1 12 1Z" fill="currentColor"/>
                                    <path d="M19 10V12C19 16.42 15.42 20 11 20H13C17.42 20 21 16.42 21 12V10H19Z" fill="currentColor"/>
                                    <path d="M5 10V12C5 16.42 8.58 20 13 20H11C6.58 20 3 16.42 3 12V10H5Z" fill="currentColor"/>
                                    <path d="M12 20V23M8 23H16" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                                </svg>
                            </button>
                            <textarea class="modal-input-field" id="modalMessageInputBottomCloud" placeholder="اسأل عن أي شيء..." rows="1"></textarea>
                            <button class="modal-send-btn" onclick="sendModalMessageFromBottomCloud()" title="إرسال">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                                    <path d="M2 21L23 12L2 3V10L17 12L2 14V21Z" fill="currentColor"/>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div id="design" class="section design-section">
            <h2>Our Design Approach</h2>
            <div class="design-gallery">
                <div class="design-card">
                    <div class="design-image">
                        <ion-icon name="home-outline"></ion-icon>
                    </div>
                    <h3>Smart Home Integration</h3>
                    <p>Seamless connection between your energy systems and smart home devices.</p>
                </div>

                <div class="design-card map-card">
                    <div class="design-image map-container">
                        <div id="embedded-map" class="embedded-map"></div>
                        <div class="map-overlay">
                            <h4>Energy.Ai Maps</h4>
                            <button class="map-expand-btn" id="expandMapBtn">
                                <ion-icon name="expand-outline"></ion-icon>
                                View Full Map
                            </button>
                        </div>
                    </div>
                    <h3>Energy.Ai Maps</h3>
                    <p>Interactive mapping interface with real-time energy data visualization and location-based analysis.</p>
                </div>
            </div>
        </div>

        <div id="contact" class="section contact-section">
            <div class="contact-header">
                <h2>Get In Touch</h2>
                <p class="contact-subtitle">Ready to transform your energy future? Let's start the conversation.</p>
            </div>

            <!-- Free Consultation Highlight Box -->
            <div class="consultation-highlight">
                <div class="consultation-content">
                    <div class="consultation-icon">
                        <ion-icon name="bulb-outline"></ion-icon>
                    </div>
                    <div class="consultation-text">
                        <h3>Get Your Free Energy Consultation</h3>
                        <p>Our experts will analyze your energy needs and provide customized solutions to reduce costs by 15-30%.</p>
                        <ul class="consultation-benefits">
                            <li>
                                <ion-icon name="checkmark-circle-outline"></ion-icon>
                                <span>Free energy audit and assessment</span>
                            </li>
                            <li>
                                <ion-icon name="checkmark-circle-outline"></ion-icon>
                                <span>Customized energy optimization plan</span>
                            </li>
                            <li>
                                <ion-icon name="checkmark-circle-outline"></ion-icon>
                                <span>ROI analysis and cost savings projection</span>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="contact-container">
                <div class="contact-form-wrapper">
                    <div class="contact-form">
                        <h3>Send us a Message</h3>
                        <form id="contactForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="contact-name">
                                        <ion-icon name="person-outline"></ion-icon>
                                        <span>Full Name</span>
                                    </label>
                                    <input type="text" id="contact-name" placeholder="Enter your full name" required>
                                </div>
                                <div class="form-group">
                                    <label for="contact-company">
                                        <ion-icon name="business-outline"></ion-icon>
                                        <span>Company</span>
                                    </label>
                                    <input type="text" id="contact-company" placeholder="Your company name">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="contact-email">
                                        <ion-icon name="mail-outline"></ion-icon>
                                        <span>Email Address</span>
                                    </label>
                                    <input type="email" id="contact-email" placeholder="<EMAIL>" required>
                                </div>
                                <div class="form-group">
                                    <label for="contact-phone">
                                        <ion-icon name="call-outline"></ion-icon>
                                        <span>Phone Number</span>
                                    </label>
                                    <input type="tel" id="contact-phone" placeholder="+962 XXX XXX XXX">
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="contact-subject">
                                    <ion-icon name="chatbubble-outline"></ion-icon>
                                    <span>Subject</span>
                                </label>
                                <select id="contact-subject" required>
                                    <option value="">Select a topic</option>
                                    <option value="consultation" selected>Free Consultation</option>
                                    <option value="general">General Inquiry</option>
                                    <option value="partnership">Partnership</option>
                                    <option value="support">Technical Support</option>
                                    <option value="demo">Request Demo</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <label for="contact-message">
                                    <ion-icon name="document-text-outline"></ion-icon>
                                    <span>Message</span>
                                </label>
                                <textarea id="contact-message" rows="6" placeholder="Tell us about your energy needs and how we can help..." required></textarea>
                            </div>

                            <button type="submit" class="submit-btn" id="contactSubmitBtn">
                                <span class="btn-text">Send Message</span>
                                <ion-icon name="paper-plane-outline"></ion-icon>
                            </button>
                            <div id="contactFormStatus" class="form-status"></div>
                        </form>
                    </div>
                </div>

                <div class="contact-info-wrapper">
                    <div class="contact-info">
                        <h3>Contact Information</h3>
                        <p class="info-description">Connect with our energy experts today</p>

                        <div class="info-items">
                            <div class="info-item">
                                <div class="info-icon">
                                    <ion-icon name="location-outline"></ion-icon>
                                </div>
                                <div class="info-content">
                                    <h4>Office Location</h4>
                                    <p>Amman, Jordan</p>
                                    <span>Middle East Headquarters</span>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-icon">
                                    <ion-icon name="mail-outline"></ion-icon>
                                </div>
                                <div class="info-content">
                                    <h4>Email Address</h4>
                                    <p><EMAIL></p>
                                    <span>We reply within 24 hours</span>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-icon">
                                    <ion-icon name="call-outline"></ion-icon>
                                </div>
                                <div class="info-content">
                                    <h4>Phone Number</h4>
                                    <p>+962 79 155 6430</p>
                                    <span>Mon - Fri, 9:00 AM - 6:00 PM</span>
                                </div>
                            </div>

                            <div class="info-item">
                                <div class="info-icon">
                                    <ion-icon name="time-outline"></ion-icon>
                                </div>
                                <div class="info-content">
                                    <h4>Business Hours</h4>
                                    <p>Sunday - Thursday</p>
                                    <span>9:00 AM - 6:00 PM (GMT+3)</span>
                                </div>
                            </div>
                        </div>

                        <div class="contact-map">
                            <div class="map-placeholder">
                                <ion-icon name="map-outline"></ion-icon>
                                <p>Interactive Map</p>
                                <button class="map-btn" onclick="openContactMap()">View Location</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Map Modal -->
    <div class="map-modal" id="mapModal">
        <div class="map-modal-content">
            <div class="map-modal-header">
                <h3>Energy.Ai Maps - Interactive View</h3>
                <button class="map-close-btn" id="closeMapModal">
                    <ion-icon name="close-outline"></ion-icon>
                </button>
            </div>
            <div id="full-map" class="full-map"></div>
        </div>
    </div>

    <footer class="footer">
        <div class="footer-content">
            <div class="footer-logo">
                <div class="footer-brand-icon"></div>
                <h2>Energy.Ai</h2>
                <p>Powering the future with intelligent solutions</p>
            </div>
            <div class="footer-links">
                <h3>Links</h3>
                <ul>
                    <li><a href="#home">Home</a></li>
                    <li><a href="#about">About</a></li>
                    <li><a href="#service" id="footerServiceLink">Services</a></li>
                    <li><a href="#design">Design</a></li>
                    <li><a href="#contact">Contact</a></li>
                    <li><a href="privacy-policy.html">Privacy Policy</a></li>
                    <li><a href="faq.html">FAQ</a></li>
                </ul>
            </div>
            <div class="footer-social">
                <h3>Follow Us</h3>
                <div class="social-icons">
                    <a href="#" aria-label="Facebook"><ion-icon name="logo-facebook"></ion-icon></a>
                    <a href="#" aria-label="Instagram"><ion-icon name="logo-instagram"></ion-icon></a>
                    <a href="#" aria-label="Twitter"><ion-icon name="logo-twitter"></ion-icon></a>
                </div>
            </div>
            <div class="footer-newsletter">
                <h3>Newsletter</h3>
                <p>Stay updated with our latest news</p>
                <div class="newsletter-form">
                    <input type="email" placeholder="Your Email Address" aria-label="Email for newsletter">
                    <button aria-label="Subscribe">
                        <ion-icon name="paper-plane-outline"></ion-icon>
                    </button>
                </div>
            </div>
        </div>
        <div class="footer-bottom">
            <p>&copy; 2025 Energy.AI. All Rights Reserved.</p>
        </div>
    </footer>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>

    <!-- Ionicons -->
    <script type="module" src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.esm.js"></script>
    <script nomodule src="https://unpkg.com/ionicons@7.1.0/dist/ionicons/ionicons.js"></script>

    <!-- Critical JavaScript - Load immediately -->
    <script>
        // Enhanced loading script with language support
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded');

            // Initialize welcome screen with language support
            initializeWelcomeScreen();
            initializeEnergyTools();

            // Hide loading screen after critical resources load
            let loadingHidden = false;

            function hideLoadingScreen() {
                if (loadingHidden) return;
                loadingHidden = true;

                console.log('Hiding loading screen...');
                const loadingScreen = document.getElementById('loadingScreen');
                if (loadingScreen) {
                    console.log('Loading screen found, adding hidden class');
                    loadingScreen.classList.add('hidden');
                    setTimeout(() => {
                        if (loadingScreen.parentNode) {
                            console.log('Removing loading screen from DOM');
                            loadingScreen.remove();
                        }
                    }, 800);
                } else {
                    console.log('Loading screen not found');
                }
            }

            // Hide after timeout
            setTimeout(hideLoadingScreen, 1500);

            // Also hide when window is fully loaded
            window.addEventListener('load', () => {
                setTimeout(hideLoadingScreen, 500);
            });

            // Make hideLoadingScreen globally available
            window.hideLoadingScreen = hideLoadingScreen;
        });

        // Skip loading function
        function skipLoading() {
            if (window.hideLoadingScreen) {
                window.hideLoadingScreen();
            }
        }

        function initializeWelcomeScreen() {
            // Add language attributes for translation
            const welcomeTitle = document.querySelector('.welcome-title');
            const brandName = document.querySelector('.brand-name');
            const welcomeSubtitle = document.querySelector('.welcome-subtitle');

            // Language attributes removed - English only

            // Add additional energy particles for enhanced visual effect
            createEnergyParticles();
        }

        function createEnergyParticles() {
            const particlesContainer = document.querySelector('.energy-particles');
            if (particlesContainer) {
                // Create additional floating particles
                for (let i = 0; i < 6; i++) {
                    const particle = document.createElement('div');
                    particle.className = 'floating-particle';
                    particle.style.cssText = `
                        position: absolute;
                        width: 3px;
                        height: 3px;
                        background: var(--secondary-color);
                        border-radius: 50%;
                        top: ${Math.random() * 100}%;
                        left: ${Math.random() * 100}%;
                        animation: particleFloat ${3 + Math.random() * 2}s ease-in-out infinite;
                        animation-delay: ${Math.random() * 2}s;
                        opacity: 0.7;
                    `;
                    particlesContainer.appendChild(particle);
                }
            }
        }

        // Energy Optimization Modal Functions
        function openEnergyOptimization() {
            const modal = document.getElementById('energyOptimizationModal');
            if (modal) {
                modal.classList.add('active');
                document.body.style.overflow = 'hidden';
                activateEnergyTool(currentEnergyTool || 'dashboard');

            }
        }

        function closeEnergyOptimization() {
            const modal = document.getElementById('energyOptimizationModal');
            if (modal) {
                modal.classList.remove('active');
                document.body.style.overflow = 'auto';
            }
        }


        // Conversation memory for Energy Optimization modal
        let energyModalConversation = [];

        function startNewChat() {
            console.warn('AI assistant has been disabled.');
        }



        async function sendModalMessage() {
            const messageInput = document.getElementById('modalMessageInput');
            if (!messageInput) return;

            const message = messageInput.value.trim();
            if (!message) return;

            switchToChatMode();
            addModalMessage(message, 'user');
            try { scrollToBottomSafe(); updateBottomPadding(); } catch (e) {}
            messageInput.value = '';
            try {
                const aiText = await requestAIResponse(message);
                addModalMessage(aiText, 'ai');
                updateBottomPadding();
            } catch (e) {
                addModalMessage('Sorry, I could not reach the AI service. Please try again.', 'ai');
            }
        }

        async function sendModalMessageFromBottom() {
            const messageInput = document.getElementById('modalMessageInputBottom');
            if (!messageInput) return;

            const message = messageInput.value.trim();
            if (!message) return;

            addModalMessage(message, 'user');
            try { scrollToBottomSafe(); updateBottomPadding(); } catch (e) {}
            messageInput.value = '';
            messageInput.style.height = 'auto';
            try {
                const aiText = await requestAIResponse(message);
                addModalMessage(aiText, 'ai');
                updateBottomPadding();
            } catch (e) {
                addModalMessage('Sorry, I could not reach the AI service. Please try again.', 'ai');
            }
        }

        function updateBottomPadding() {
            const input = document.getElementById('bottomInputContainer');
            const scroller = document.querySelector('.modal-chat-area');
            const lists = document.querySelectorAll('.messages-list, .chat-content-area');
            const h = input ? input.getBoundingClientRect().height : 0;
            const pad = Math.min(window.innerHeight * 0.6, Math.max(160, Math.round(h + 56)));
            if (scroller) scroller.style.paddingBottom = pad + 'px';
            lists.forEach(el => el.style.paddingBottom = pad + 'px');
            ensureInputSpacer();
        }

        function ensureInputSpacer() {
            try {
                const list = document.querySelector('.messages-list');
                const input = document.getElementById('bottomInputContainer');
                if (!list || !input) return;
                let spacer = list.querySelector('#inputSpacer');
                const h = input.getBoundingClientRect().height || 0;
                const need = Math.round(h + 48);
                if (!spacer) {
                    spacer = document.createElement('div');
                    spacer.id = 'inputSpacer';
                    spacer.style.width = '100%';
                    spacer.style.height = need + 'px';
                    spacer.style.pointerEvents = 'none';
                    list.appendChild(spacer);
                } else {
                    spacer.style.height = need + 'px';
                    // Always ensure spacer stays as the last element
                    // so it doesn't appear between consecutive messages
                    list.appendChild(spacer);
                }
            } catch (e) {}
        }

        function switchToChatMode() {
            const welcomeContent = document.getElementById('welcomeContent');
            const centerInput = document.getElementById('centerInputContainer');
            const bottomInput = document.getElementById('bottomInputContainer');
            const chatContentArea = document.getElementById('chatContentArea');

            // Hide center input if it exists
            if (centerInput) {
                centerInput.style.display = 'none';
            }

            // Change welcome content styling for messages
            welcomeContent.style.textAlign = 'left';
            welcomeContent.style.maxWidth = '100%';
            welcomeContent.style.display = 'flex';
            welcomeContent.style.flexDirection = 'column';
            welcomeContent.style.alignItems = 'center';
            welcomeContent.style.justifyContent = 'flex-start';
            welcomeContent.style.height = 'auto';
            welcomeContent.style.paddingBottom = '0px';

            // Change chat area styling for messages
            chatContentArea.style.justifyContent = 'flex-start';
            chatContentArea.style.textAlign = 'left';
            chatContentArea.style.alignItems = 'stretch';
            chatContentArea.style.padding = '20px';

            // Show bottom input
            bottomInput.style.display = 'flex';

            // Ensure enough bottom space so last message stays above the input bar (dynamic)
            updateBottomPadding();
        }

        // Helper to scroll to bottom while keeping last message above the fixed input bar
        function scrollToBottomSafe() {
            const scroller = document.querySelector('.modal-chat-area');
            if (!scroller) return;
            const input = document.getElementById('bottomInputContainer');
            const inputH = input ? input.getBoundingClientRect().height : 160;
            const extra = 24; // small breathing space
            const target = Math.max(0, scroller.scrollHeight - scroller.clientHeight - inputH - extra);
            scroller.scrollTo({ top: target, behavior: 'smooth' });
        }

        let currentEnergyTool = 'dashboard';

        function seededRandom(seed) {
            const x = Math.sin(seed * 12.9898) * 43758.5453;
            return x - Math.floor(x);
        }

        function formatNumber(value, options) {
            const num = Number.isFinite(value) ? value : 0;
            return num.toLocaleString('en-US', options);
        }

        function renderEnergyDashboard(container) {
            const now = new Date();
            const baseDaily = 126;
            const solarFactor = 0.75 + Math.sin((now.getHours() / 24) * Math.PI * 2) * 0.18;
            const productionToday = Math.round(baseDaily * solarFactor * 10) / 10;
            const demand = Math.round(productionToday * (0.78 + seededRandom(now.getDate()) * 0.18) * 10) / 10;
            const savings = Math.round(productionToday * 0.22);
            const co2 = Math.round(productionToday * 0.56);
            const availability = Math.round((97.2 + seededRandom(now.getMonth() + 1) * 2.1) * 10) / 10;

            container.innerHTML = `
                <div class="tool-header">
                    <h2>لوحة التحكم الذكية</h2>
                    <p>نظرة حية على أداء النظام الشمسي، بما في ذلك الإنتاج اللحظي، الطلب الحالي، والتأثير البيئي خلال اليوم.</p>
                </div>
                <div class="tool-stat-grid">
                    <div class="tool-stat-card">
                        <h3>إنتاج اليوم</h3>
                        <div class="tool-stat-value" id="dashboardEnergyToday">${formatNumber(productionToday)} kWh</div>
                        <div class="tool-stat-trend">+${formatNumber(Math.round(productionToday * 0.07))} kWh مقارنة بالأمس</div>
                    </div>
                    <div class="tool-stat-card">
                        <h3>الطلب الحالي</h3>
                        <div class="tool-stat-value" id="dashboardDemand">${formatNumber(demand)} kWh</div>
                        <div class="tool-stat-trend">الذكاء الاصطناعي يخفّض الذروة بنسبة 18%</div>
                    </div>
                    <div class="tool-stat-card">
                        <h3>التوفير التراكمي</h3>
                        <div class="tool-stat-value" id="dashboardSavings">${formatNumber(savings)} JD</div>
                        <div class="tool-stat-trend">تراكم منذ بداية الشهر</div>
                    </div>
                    <div class="tool-stat-card">
                        <h3>خفض الانبعاثات</h3>
                        <div class="tool-stat-value" id="dashboardCO2">${formatNumber(co2)} كجم</div>
                        <div class="tool-stat-trend">متوقع تفاديه خلال هذا الأسبوع</div>
                    </div>
                </div>
                <div>
                    <h3 class="tool-section-title">توقع الإشعاع الشمسي للأسبوع</h3>
                    <div class="tool-weekly-bars" id="dashboardWeeklyBars"></div>
                    <p class="tool-note">التوقعات تعتمد على بيانات الطقس والغيوم المتاحة حاليًا.</p>
                </div>
                <div class="tool-progress">
                    <span>جاهزية النظام</span>
                    <div class="tool-progress-bar">
                        <div class="tool-progress-fill" id="dashboardAvailability"></div>
                    </div>
                    <span id="dashboardAvailabilityLabel">${availability}% وقت تشغيل مضمون</span>
                </div>
            `;

            const weeklyBars = container.querySelector('#dashboardWeeklyBars');
            if (weeklyBars) {
                const days = ['سب', 'أح', 'إث', 'ثل', 'أر', 'خم', 'جم'];
                const values = days.map((_, index) => {
                    const base = productionToday * (0.85 + seededRandom(now.getDate() + index + 1) * 0.3);
                    return Math.round(Math.max(70, base));
                });
                const maxValue = Math.max(...values, 100);
                values.forEach((value, index) => {
                    const bar = document.createElement('div');
                    bar.className = 'tool-weekly-bar';
                    const fill = document.createElement('div');
                    fill.className = 'tool-weekly-bar-fill';
                    fill.style.height = Math.max(8, Math.round((value / maxValue) * 100)) + '%';
                    bar.appendChild(fill);
                    const label = document.createElement('div');
                    label.className = 'tool-weekly-bar-day';
                    label.textContent = days[index];
                    bar.appendChild(label);
                    weeklyBars.appendChild(bar);
                });
            }

            const availabilityFill = container.querySelector('#dashboardAvailability');
            if (availabilityFill) {
                availabilityFill.style.width = Math.min(100, Math.max(0, availability)) + '%';
            }
        }

        const WEATHER_API_KEY = 'fdca9387bb35438eaba110808251009';

        function renderLiveSimulation(container) {
            container.innerHTML = `
                <div class="tool-header">
                    <h2>المحاكاة الحية والتحليل المقارن</h2>
                    <p>اختر موقع المشروع واضبط مواصفات النظام للحصول على توقعات لحظية، تحليل أداء، ورؤية واضحة لتأثير الطقس.</p>
                </div>
                <div class="live-sim-grid">
                    <div class="live-sim-settings">
                        <div>
                            <h3 class="live-sim-title">إعدادات النظام</h3>
                            <p class="live-sim-subtitle">حدد موقع المشروع ثم ابدأ المحاكاة لرصد الأداء الحقيقي مقابل التوقعات.</p>
                        </div>
                        <div class="live-sim-map" id="liveSimMap"></div>
                        <div class="live-sim-coordinates">
                            <span>خط العرض<br><strong id="liveSimLat">31.9539°</strong></span>
                            <span>خط الطول<br><strong id="liveSimLon">35.9106°</strong></span>
                        </div>
                        <form class="live-sim-form" id="liveSimForm">
                            <label>حجم النظام (kWp)
                                <input type="number" id="liveSimSystemSize" value="5" min="0.5" step="0.1" required>
                            </label>
                            <label>سعر الكيلوواط/ساعة (دينار)
                                <input type="number" id="liveSimTariff" value="0.12" min="0" step="0.01" required>
                            </label>
                            <label>زاوية ميل الألواح (°)
                                <input type="number" id="liveSimTilt" value="30" min="0" max="90" step="1" required>
                            </label>
                            <label>زاوية الاتجاه (° - الجنوب=180)
                                <input type="number" id="liveSimAzimuth" value="180" min="0" max="360" step="1" required>
                            </label>
                            <button type="submit" class="tool-button" id="liveSimToggleBtn">بدء المحاكاة</button>
                        </form>
                        <div class="live-sim-status" id="liveSimStatus">
                            جاهز لبدء المحاكاة. اختر موقعًا جديدًا بالضغط على الخريطة، ثم اضغط على "بدء المحاكاة".
                        </div>
                    </div>
                    <div class="live-sim-visual">
                        <div class="live-sim-stats">
                            <div class="live-sim-stat-card">
                                <div class="live-sim-stat-label">الإنتاج اللحظي</div>
                                <div class="live-sim-stat-value" id="liveSimLivePower">--</div>
                                <div class="live-sim-stat-label">واط</div>
                            </div>
                            <div class="live-sim-stat-card">
                                <div class="live-sim-stat-label">الإنتاج المتوقع</div>
                                <div class="live-sim-stat-value" id="liveSimForecastPower">--</div>
                                <div class="live-sim-stat-label">واط</div>
                            </div>
                            <div class="live-sim-stat-card">
                                <div class="live-sim-stat-label">الإنتاج المثالي</div>
                                <div class="live-sim-stat-value" id="liveSimIdealPower">--</div>
                                <div class="live-sim-stat-label">واط</div>
                            </div>
                            <div class="live-sim-stat-card">
                                <div class="live-sim-stat-label">التوفير اللحظي</div>
                                <div class="live-sim-stat-value" id="liveSimSavings">--</div>
                                <div class="live-sim-stat-label">دينار/ساعة</div>
                            </div>
                            <div class="live-sim-stat-card">
                                <div class="live-sim-stat-label">كفاءة الأداء</div>
                                <div class="live-sim-stat-value" id="liveSimEfficiency">--</div>
                                <div class="live-sim-stat-label">مقارنة بالسماء الصافية</div>
                            </div>
                        </div>
                        <div class="live-sim-weather">
                            <div class="live-sim-weather-card">
                                <div class="live-sim-stat-label">درجة الحرارة</div>
                                <div class="live-sim-stat-value" id="liveSimTemperature">--</div>
                                <div class="live-sim-stat-label">°C</div>
                            </div>
                            <div class="live-sim-weather-card">
                                <div class="live-sim-stat-label">مؤشر الأشعة فوق البنفسجية</div>
                                <div class="live-sim-stat-value" id="liveSimUV">--</div>
                                <div class="live-sim-stat-label">UV</div>
                            </div>
                            <div class="live-sim-weather-card">
                                <div class="live-sim-stat-label">نسبة الغيوم</div>
                                <div class="live-sim-stat-value" id="liveSimClouds">--</div>
                                <div class="live-sim-stat-label">%</div>
                            </div>
                        </div>
                        <div class="live-sim-analysis" id="liveSimAnalysis">
                            تنتظر المنصة بيانات المحاكاة. بعد بدء التشغيل ستظهر توصيات الذكاء الاصطناعي حول العوامل المؤثرة في الأداء.
                        </div>
                        <div class="live-sim-summary">
                            <div class="live-sim-summary-card">
                                <div class="live-sim-stat-label">إنتاج اليوم المتوقع</div>
                                <div class="live-sim-stat-value" id="liveSimDailyEnergy">--</div>
                                <div class="live-sim-stat-label">kWh</div>
                            </div>
                            <div class="live-sim-summary-card">
                                <div class="live-sim-stat-label">العائد المتوقع</div>
                                <div class="live-sim-stat-value" id="liveSimDailyRevenue">--</div>
                                <div class="live-sim-stat-label">دينار</div>
                            </div>
                            <div class="live-sim-summary-card">
                                <div class="live-sim-stat-label">أفضل ساعة قادمة</div>
                                <div class="live-sim-stat-value" id="liveSimBestHour">--</div>
                                <div class="live-sim-stat-label">ذروة متوقعة</div>
                            </div>
                        </div>
                        <div class="live-sim-charts">
                            <div class="live-sim-chart-card">
                                <h3>منحنى الإنتاج المتوقع خلال اليوم</h3>
                                <canvas id="liveSimDailyChart" height="240"></canvas>
                            </div>
                            <div class="live-sim-chart-card">
                                <h3>الأداء مقابل التوقعات (آخر قراءات)</h3>
                                <canvas id="liveSimPowerChart" height="240"></canvas>
                            </div>
                            <div class="live-sim-chart-card">
                                <h3>تأثير العوامل الجوية</h3>
                                <canvas id="liveSimWeatherChart" height="240"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            setupLiveSimulationTool(container);
        }

        function ensureChartJs() {
            if (window.Chart) {
                return Promise.resolve();
            }
            if (window.__chartJsLoading) {
                return window.__chartJsLoading;
            }
            window.__chartJsLoading = new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = 'https://cdn.jsdelivr.net/npm/chart.js@4.4.7/dist/chart.umd.min.js';
                script.async = true;
                script.onload = () => resolve();
                script.onerror = () => reject(new Error('فشل تحميل Chart.js'));
                document.head.appendChild(script);
            });
            return window.__chartJsLoading;
        }

        function setupLiveSimulationTool(container) {
            const defaults = { lat: 31.9539, lon: 35.9106 };
            const state = window.liveSimulationController || {
                lat: defaults.lat,
                lon: defaults.lon,
                running: false,
                intervalId: null,
                powerHistory: [],
                weatherHistory: [],
                charts: {},
            };

            if (state.intervalId) {
                clearInterval(state.intervalId);
                state.intervalId = null;
            }

            state.container = container;
            state.running = false;
            state.powerHistory = [];
            state.weatherHistory = [];

            window.liveSimulationController = state;

            const mapElement = container.querySelector('#liveSimMap');
            const latLabel = container.querySelector('#liveSimLat');
            const lonLabel = container.querySelector('#liveSimLon');
            const statusElement = container.querySelector('#liveSimStatus');
            const form = container.querySelector('#liveSimForm');
            const inputs = Array.from(form.querySelectorAll('input'));
            const toggleBtn = container.querySelector('#liveSimToggleBtn');
            const stats = {
                live: container.querySelector('#liveSimLivePower'),
                forecast: container.querySelector('#liveSimForecastPower'),
                ideal: container.querySelector('#liveSimIdealPower'),
                savings: container.querySelector('#liveSimSavings'),
                efficiency: container.querySelector('#liveSimEfficiency'),
            };
            const weather = {
                temp: container.querySelector('#liveSimTemperature'),
                uv: container.querySelector('#liveSimUV'),
                clouds: container.querySelector('#liveSimClouds'),
            };
            const summary = {
                energy: container.querySelector('#liveSimDailyEnergy'),
                revenue: container.querySelector('#liveSimDailyRevenue'),
                bestHour: container.querySelector('#liveSimBestHour'),
            };
            const analysisElement = container.querySelector('#liveSimAnalysis');

            const chartElements = {
                power: container.querySelector('#liveSimPowerChart'),
                weather: container.querySelector('#liveSimWeatherChart'),
                daily: container.querySelector('#liveSimDailyChart'),
            };

            enableInputs(true);
            toggleBtn.disabled = false;
            toggleBtn.textContent = 'بدء المحاكاة';

            function updateCoordinatesDisplay() {
                latLabel.textContent = state.lat.toFixed(4) + '°';
                lonLabel.textContent = state.lon.toFixed(4) + '°';
            }

            function setStatus(message, isError) {
                statusElement.textContent = message;
                statusElement.classList.toggle('live-sim-error', !!isError);
            }

            function enableInputs(enabled) {
                inputs.forEach(input => {
                    input.disabled = !enabled;
                });
            }

            function getFormValues() {
                return {
                    systemSize: Math.max(0.5, parseFloat(form.querySelector('#liveSimSystemSize').value) || 5),
                    tariff: Math.max(0, parseFloat(form.querySelector('#liveSimTariff').value) || 0.12),
                    tilt: Math.max(0, Math.min(90, parseFloat(form.querySelector('#liveSimTilt').value) || 30)),
                    azimuth: Math.max(0, Math.min(360, parseFloat(form.querySelector('#liveSimAzimuth').value) || 180)),
                };
            }

            function cleanupCharts() {
                Object.values(state.charts || {}).forEach(chart => {
                    if (chart && typeof chart.destroy === 'function') chart.destroy();
                });
                state.charts = {};
            }

            function stopSimulation(dueToError) {
                if (state.intervalId) {
                    clearInterval(state.intervalId);
                    state.intervalId = null;
                }
                state.running = false;
                toggleBtn.textContent = 'بدء المحاكاة';
                enableInputs(true);
                toggleBtn.disabled = false;
                if (!dueToError) {
                    setStatus('تم إيقاف المحاكاة. يمكنك تعديل الإعدادات والبدء من جديد.', false);
                }
            }

            if (state.stopSimulationHook) {
                try { state.stopSimulationHook(); } catch (e) {}
            }
            state.stopSimulationHook = () => stopSimulation(true);

            function updatePowerHistory(point) {
                state.powerHistory.push(point);
                if (state.powerHistory.length > 15) {
                    state.powerHistory.shift();
                }
            }

            function updateWeatherHistory(point) {
                state.weatherHistory.push(point);
                if (state.weatherHistory.length > 15) {
                    state.weatherHistory.shift();
                }
            }

            function initMap() {
                if (!mapElement || !window.L) {
                    setStatus('تعذر تحميل خريطة Leaflet. تحقق من الاتصال.', true);
                    return;
                }

                if (state.map) {
                    try {
                        state.map.remove();
                    } catch (e) {}
                }

                state.map = L.map(mapElement).setView([state.lat, state.lon], 9);
                L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                    maxZoom: 18,
                    attribution: '&copy; OpenStreetMap contributors'
                }).addTo(state.map);

                state.marker = L.marker([state.lat, state.lon], { draggable: true }).addTo(state.map);
                state.marker.on('dragend', event => {
                    const { lat, lng } = event.target.getLatLng();
                    updateLocation(lat, lng, true);
                });

                state.map.on('click', event => {
                    updateLocation(event.latlng.lat, event.latlng.lng, true);
                });
            }

            function updateLocation(lat, lon, notify) {
                state.lat = parseFloat(lat.toFixed(5));
                state.lon = parseFloat(lon.toFixed(5));
                updateCoordinatesDisplay();
                if (state.marker) {
                    state.marker.setLatLng([state.lat, state.lon]);
                }
                if (notify) {
                    setStatus('تم تحديث موقع المشروع. سيتم استخدام الإحداثيات الجديدة عند التحديث التالي.', false);
                }
            }

            async function startSimulation() {
                const values = getFormValues();
                state.values = values;
                enableInputs(false);
                toggleBtn.disabled = true;
                toggleBtn.textContent = 'جارٍ التشغيل...';
                setStatus('جارٍ تحميل مكتبة الرسوم البيانية واستدعاء بيانات الطقس...', false);

                try {
                    await ensureChartJs();
                } catch (error) {
                    console.error(error);
                    setStatus('تعذر تحميل مكتبة الرسوم البيانية. تحقق من الاتصال بالإنترنت.', true);
                    enableInputs(true);
                    toggleBtn.textContent = 'بدء المحاكاة';
                    toggleBtn.disabled = false;
                    return;
                }

                cleanupCharts();
                state.powerHistory = [];
                state.weatherHistory = [];

                async function runCycle() {
                    try {
                        await executeSimulationStep(values);
                        setStatus('المحاكاة نشطة. يتم تحديث البيانات كل 60 ثانية.', false);
                        toggleBtn.disabled = false;
                        toggleBtn.textContent = 'إيقاف المحاكاة';
                    } catch (error) {
                        console.error('live simulation error', error);
                        setStatus('حدث خطأ أثناء جلب البيانات. تم إيقاف المحاكاة مؤقتًا.', true);
                        stopSimulation(true);
                        throw error;
                    }
                }

                try {
                    await runCycle();
                } catch (error) {
                    return;
                }

                state.running = true;
                state.intervalId = setInterval(() => {
                    runCycle().catch(() => {});
                }, 60000);
                toggleBtn.disabled = false;
                toggleBtn.textContent = 'إيقاف المحاكاة';
            }

            async function executeSimulationStep(values) {
                if (!WEATHER_API_KEY) {
                    throw new Error('لم يتم إعداد مفتاح واجهة الطقس.');
                }

                const lat = state.lat;
                const lon = state.lon;
                const query = encodeURIComponent((lat ?? 0) + ',' + (lon ?? 0));
                const url = 'https://api.weatherapi.com/v1/forecast.json?key=' + WEATHER_API_KEY + '&q=' + query + '&days=2&aqi=no&alerts=no';
                const response = await fetch(url, { cache: 'no-store' });
                if (!response.ok) {
                    throw new Error('فشل طلب بيانات الطقس من WeatherAPI');
                }
                const data = await response.json();

                const forecastDays = Array.isArray(data.forecast?.forecastday) ? data.forecast.forecastday : [];
                const hourlyRecords = [];
                forecastDays.forEach(day => {
                    if (Array.isArray(day?.hour)) {
                        day.hour.forEach(hour => {
                            hourlyRecords.push({ ...hour, date: day.date });
                        });
                    }
                });

                if (!hourlyRecords.length) {
                    throw new Error('لا توجد بيانات توقع متاحة من مزود الطقس.');
                }

                const currentStamp = data.current?.last_updated || hourlyRecords[0].time;
                const now = currentStamp ? new Date(currentStamp.replace(' ', 'T')) : new Date();
                let index = hourlyRecords.findIndex(hour => hour.time === currentStamp);
                if (index === -1) {
                    const nowTs = now.getTime();
                    let bestDiff = Number.POSITIVE_INFINITY;
                    hourlyRecords.forEach((hour, idx) => {
                        const diff = Math.abs(new Date(hour.time.replace(' ', 'T')).getTime() - nowTs);
                        if (diff < bestDiff) {
                            bestDiff = diff;
                            index = idx;
                        }
                    });
                    if (index === -1) index = 0;
                }

                const nextIndex = Math.min(index + 1, hourlyRecords.length - 1);
                const currentHour = hourlyRecords[index];
                const nextHour = hourlyRecords[nextIndex];

                const uv = currentHour?.uv ?? data.current?.uv ?? 0;
                const nextUv = nextHour?.uv ?? uv;
                const cloud = currentHour?.cloud ?? data.current?.cloud ?? 0;
                const nextCloud = nextHour?.cloud ?? cloud;
                const temperature = data.current?.temp_c ?? currentHour?.temp_c ?? 25;
                const nextTemperature = nextHour?.temp_c ?? temperature;
                const irradiance = normalizeSolarRadiation(currentHour);
                const nextIrradiance = normalizeSolarRadiation(nextHour) ?? irradiance;

                const idealPower = values.systemSize * 1000;
                const livePower = computePowerFromWeather({
                    irradiance,
                    uv,
                    cloud,
                    temperature,
                    idealPower,
                    tilt: values.tilt,
                    azimuth: values.azimuth,
                    isDay: currentHour?.is_day,
                });
                const forecastPower = computePowerFromWeather({
                    irradiance: nextIrradiance,
                    uv: nextUv,
                    cloud: nextCloud,
                    temperature: nextTemperature,
                    idealPower,
                    tilt: values.tilt,
                    azimuth: values.azimuth,
                    isDay: nextHour?.is_day,
                });

                const efficiency = idealPower > 0 ? Math.min(150, (livePower / idealPower) * 100) : 0;
                const savings = (livePower / 1000) * values.tariff;

                const timeLabel = now.toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit' });
                updatePowerHistory({ label: timeLabel, live: Math.round(livePower), forecast: Math.round(forecastPower), ideal: Math.round(idealPower) });
                updateWeatherHistory({ label: timeLabel, uv: parseFloat((uv || 0).toFixed(1)), cloud: Math.round(cloud || 0) });

                stats.live.textContent = formatNumber(livePower, { maximumFractionDigits: 0 });
                stats.forecast.textContent = formatNumber(forecastPower, { maximumFractionDigits: 0 });
                stats.ideal.textContent = formatNumber(idealPower, { maximumFractionDigits: 0 });
                stats.savings.textContent = formatNumber(savings, { maximumFractionDigits: 3 });
                stats.efficiency.textContent = formatNumber(efficiency, { maximumFractionDigits: 1 }) + '%';

                weather.temp.textContent = formatNumber(temperature, { maximumFractionDigits: 1 });
                weather.uv.textContent = formatNumber(uv, { maximumFractionDigits: 1 });
                weather.clouds.textContent = formatNumber(cloud, { maximumFractionDigits: 0 });

                const dailyData = buildDailyForecastSeries(data, values, idealPower, now);
                summary.energy.textContent = formatNumber(dailyData.totalEnergyKwh, { maximumFractionDigits: 2 });
                summary.revenue.textContent = formatNumber(dailyData.totalRevenue, { maximumFractionDigits: 2 });
                summary.bestHour.textContent = dailyData.bestHourLabel || '--';

                analysisElement.textContent = generatePerformanceInsight({
                    efficiency,
                    cloud,
                    temperature,
                    uv,
                    tilt: values.tilt,
                    optimalTilt: dailyData.optimalTilt,
                });

                await renderLiveSimCharts(state, chartElements, dailyData.series);
            }

            function normalizeSolarRadiation(hourRecord) {
                if (!hourRecord) return null;
                const candidates = [
                    hourRecord.solarradiance,
                    hourRecord.solar_radiation,
                    hourRecord.solarRadiation,
                    hourRecord.solar_rad,
                    hourRecord.short_rad,
                ];
                for (const candidate of candidates) {
                    const value = parseFloat(candidate);
                    if (Number.isFinite(value) && value > 0) {
                        return value;
                    }
                }
                return null;
            }

            function computePowerFromWeather({ irradiance, uv, cloud, temperature, idealPower, tilt, azimuth, isDay }) {
                if (isDay === 0) {
                    return 0;
                }

                const effectiveIrradiance = (() => {
                    if (Number.isFinite(irradiance) && irradiance > 0) {
                        return irradiance;
                    }
                    const uvContribution = (uv || 0) * 120;
                    const base = 650 * Math.max(0.15, 1 - (cloud || 0) / 120);
                    return Math.max(0, uvContribution + base);
                })();

                const optimalTilt = Math.min(45, Math.max(10, Math.abs(state.lat) * 0.76 + 3.5));
                const irradianceFactor = Math.min(1.2, effectiveIrradiance / 950);
                const cloudFactor = Math.max(0.2, 1 - (cloud || 0) / 120);
                const tiltLoss = 1 - Math.min(0.35, Math.abs((tilt ?? optimalTilt) - optimalTilt) * 0.008);
                const orientationLoss = 1 - Math.min(0.35, Math.abs((azimuth ?? 180) - 180) * 0.002);
                const temperatureLoss = 1 - Math.min(0.45, Math.max(0, (temperature ?? 25) - 25) * 0.0055);
                const uvBoost = 1 + Math.min(0.08, (uv || 0) / 100);
                const normalized = irradianceFactor * cloudFactor * tiltLoss * orientationLoss * temperatureLoss * uvBoost;
                return Math.max(0, idealPower * normalized);
            }

            function buildDailyForecastSeries(data, values, idealPower, now) {
                const forecastDays = Array.isArray(data.forecast?.forecastday) ? data.forecast.forecastday : [];
                const targetDate = now.toISOString().slice(0, 10);
                const dayRecord = forecastDays.find(day => day.date === targetDate) || forecastDays[0];
                const optimalTilt = Math.min(45, Math.max(10, Math.abs(state.lat) * 0.76 + 3.5));

                if (!dayRecord || !Array.isArray(dayRecord.hour)) {
                    return {
                        series: [],
                        totalEnergyKwh: 0,
                        totalRevenue: 0,
                        bestHourLabel: '',
                        optimalTilt,
                    };
                }

                let totalEnergyWh = 0;
                let bestHourPower = 0;
                let bestHourLabel = '';
                const points = dayRecord.hour.map(hour => {
                    const power = computePowerFromWeather({
                        irradiance: normalizeSolarRadiation(hour),
                        uv: hour.uv,
                        cloud: hour.cloud,
                        temperature: hour.temp_c,
                        idealPower,
                        tilt: values.tilt,
                        azimuth: values.azimuth,
                        isDay: hour.is_day,
                    });
                    totalEnergyWh += power;
                    if (power > bestHourPower) {
                        bestHourPower = power;
                        bestHourLabel = hour.time.slice(11, 16);
                    }
                    return {
                        label: hour.time.slice(11, 16),
                        powerKw: power / 1000,
                    };
                });

                return {
                    series: points,
                    totalEnergyKwh: totalEnergyWh / 1000,
                    totalRevenue: (totalEnergyWh / 1000) * values.tariff,
                    bestHourLabel,
                    optimalTilt,
                };
            }
            function generatePerformanceInsight({ efficiency, cloud, temperature, uv, tilt, optimalTilt }) {
                const insights = [];
                if (efficiency >= 90) {
                    insights.push('الأداء ممتاز ويقارب القدرة المثالية للنظام.');
                } else if (efficiency >= 60) {
                    insights.push('الأداء جيد لكن يمكن تحسينه بتحسين الصيانة أو ضبط الانحراف الطفيف.');
                } else {
                    insights.push('الكفاءة منخفضة حاليًا، ينصح بمراجعة الظروف البيئية وميل الألواح.');
                }

                if (cloud > 60) {
                    insights.push('نسبة الغيوم المرتفعة تقلل من الإشعاع الواصل للألواح. راقب التوقعات خلال الساعات المقبلة.');
                }

                if (temperature > 35) {
                    insights.push('درجة الحرارة المرتفعة قد تخفض كفاءة الخلايا، تأكد من التهوية المناسبة.');
                }

                if (uv < 3) {
                    insights.push('مؤشر UV منخفض حاليًا مما يعني طاقة شمسية محدودة، توقع تحسنًا مع ارتفاع الشمس.');
                }

                const tiltDiff = Math.abs(tilt - optimalTilt);
                if (tiltDiff > 8) {
                    insights.push(`الزاوية الحالية (${tilt.toFixed(0)}°) بعيدة عن الزاوية المثلى (${optimalTilt.toFixed(0)}°). ضع في الحسبان تعديل الميل خلال الصيانة القادمة.`);
                }

                return insights.join(' ');
            }

        async function renderLiveSimCharts(state, chartElements, dailySeries) {
            await ensureChartJs();

            const labels = state.powerHistory.map(item => item.label);
                const liveData = state.powerHistory.map(item => item.live);
                const forecastData = state.powerHistory.map(item => item.forecast);
                const idealData = state.powerHistory.map(item => item.ideal);

                if (chartElements.power) {
                    if (!state.charts.power) {
                        state.charts.power = new Chart(chartElements.power.getContext('2d'), {
                            type: 'line',
                            data: {
                                labels,
                                datasets: [
                                    {
                                        label: 'إنتاج فعلي',
                                        data: liveData,
                                        borderColor: '#1976d2',
                                        backgroundColor: 'rgba(25, 118, 210, 0.18)',
                                        borderWidth: 2,
                                        tension: 0.35,
                                        fill: true,
                                    },
                                    {
                                        label: 'توقعات الطقس',
                                        data: forecastData,
                                        borderColor: '#3b82f6',
                                        borderDash: [6, 4],
                                        borderWidth: 2,
                                        tension: 0.35,
                                        fill: false,
                                    },
                                    {
                                        label: 'القدرة المثالية',
                                        data: idealData,
                                        borderColor: 'rgba(255,255,255,0.35)',
                                        borderDash: [3, 3],
                                        borderWidth: 1.5,
                                        tension: 0.35,
                                        fill: false,
                                    }
                                ],
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                scales: {
                                    y: {
                                        ticks: { color: 'rgba(255,255,255,0.7)' },
                                        grid: { color: 'rgba(255,255,255,0.08)' },
                                        title: { display: true, text: 'واط', color: 'rgba(255,255,255,0.7)' },
                                    },
                                    x: {
                                        ticks: { color: 'rgba(255,255,255,0.6)' },
                                        grid: { display: false },
                                    }
                                },
                                plugins: {
                                    legend: {
                                        labels: { color: 'rgba(255,255,255,0.8)' }
                                    }
                                }
                            }
                        });
                    } else {
                        state.charts.power.data.labels = labels;
                        state.charts.power.data.datasets[0].data = liveData;
                        state.charts.power.data.datasets[1].data = forecastData;
                        state.charts.power.data.datasets[2].data = idealData;
                        state.charts.power.update('none');
                    }
                }

                if (chartElements.weather) {
                    const weatherLabels = state.weatherHistory.map(item => item.label);
                    const uvData = state.weatherHistory.map(item => item.uv);
                    const cloudData = state.weatherHistory.map(item => item.cloud);

                    if (!state.charts.weather) {
                        state.charts.weather = new Chart(chartElements.weather.getContext('2d'), {
                            type: 'bar',
                            data: {
                                labels: weatherLabels,
                                datasets: [
                                    {
                                        type: 'line',
                                        label: 'مؤشر UV',
                                        data: uvData,
                                        borderColor: '#facc15',
                                        backgroundColor: 'rgba(250, 204, 21, 0.3)',
                                        yAxisID: 'uv',
                                        tension: 0.3,
                                        fill: true,
                                    },
                                    {
                                        type: 'bar',
                                        label: 'نسبة الغيوم',
                                        data: cloudData,
                                        backgroundColor: 'rgba(148, 163, 184, 0.45)',
                                        borderColor: 'rgba(148, 163, 184, 0.8)',
                                        borderWidth: 1,
                                        yAxisID: 'cloud',
                                    }
                                ],
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                scales: {
                                    uv: {
                                        position: 'left',
                                        ticks: { color: '#facc15' },
                                        grid: { color: 'rgba(255,255,255,0.08)' },
                                    },
                                    cloud: {
                                        position: 'right',
                                        ticks: { color: 'rgba(255,255,255,0.6)', callback: v => v + '%' },
                                        grid: { display: false },
                                        min: 0,
                                        max: 100,
                                    },
                                    x: {
                                        ticks: { color: 'rgba(255,255,255,0.6)' },
                                        grid: { display: false }
                                    }
                                },
                                plugins: {
                                    legend: {
                                        labels: { color: 'rgba(255,255,255,0.8)' }
                                    }
                                }
                            }
                        });
                    } else {
                        state.charts.weather.data.labels = weatherLabels;
                        state.charts.weather.data.datasets[0].data = uvData;
                        state.charts.weather.data.datasets[1].data = cloudData;
                        state.charts.weather.update('none');
                    }
                }

            if (chartElements.daily) {
                const labels = dailySeries.map(point => point.label);
                const data = dailySeries.map(point => point.powerKw);

                if (!state.charts.daily) {
                        state.charts.daily = new Chart(chartElements.daily.getContext('2d'), {
                            type: 'line',
                            data: {
                                labels,
                                datasets: [
                                    {
                                        label: 'قدرة متوقعة (kW)',
                                        data,
                                        borderColor: '#22c55e',
                                        backgroundColor: 'rgba(34, 197, 94, 0.2)',
                                        borderWidth: 2,
                                        fill: true,
                                        tension: 0.3,
                                    }
                                ]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                scales: {
                                    y: {
                                        ticks: { color: 'rgba(255,255,255,0.7)' },
                                        grid: { color: 'rgba(255,255,255,0.08)' },
                                    },
                                    x: {
                                        ticks: { color: 'rgba(255,255,255,0.6)' },
                                        grid: { display: false },
                                    }
                                },
                                plugins: {
                                    legend: { labels: { color: 'rgba(255,255,255,0.8)' } }
                                }
                            }
                        });
                    } else {
                        state.charts.daily.data.labels = labels;
                        state.charts.daily.data.datasets[0].data = data;
                        state.charts.daily.update('none');
                    }
                }
            }

            form.addEventListener('submit', event => {
                event.preventDefault();
                if (state.running) {
                    stopSimulation(false);
                } else {
                    startSimulation();
                }
            });

            updateCoordinatesDisplay();
            initMap();
            setStatus('جاهز لبدء المحاكاة. اختر موقعًا أو استخدم الإعدادات الحالية.', false);

            // توقف تلقائي عند مغادرة الصفحة من أجل الطاقة.
            state.onDeactivate = () => stopSimulation(true);
        }

        function renderSystemSizing(container) {
            container.innerHTML = `
                <div class="tool-header">
                    <h2>🧮 حاسبة حجم النظام الفني والمالي المتقدمة</h2>
                    <p>أدخل بيانات الاستهلاك أو قيمة الفاتورة، واختر المعايير الفنية لتحصل على تصميم متكامل وتحليل مالي موثق مع توصيات ذكية.</p>
                </div>

                <div class="system-sizing-layout">
                    <!-- قسم الإدخال -->
                    <div class="system-sizing-card input-section">
                        <div class="card-header">
                            <h3>📊 بيانات المشروع</h3>
                        </div>

                        <!-- تبويبات نوع الحساب -->
                        <div class="system-sizing-tabs">
                            <button type="button" class="tab-button active" data-tab="consumption">
                                <span class="tab-icon">⚡</span>
                                <span>حسب الاستهلاك</span>
                            </button>
                            <button type="button" class="tab-button" data-tab="bill">
                                <span class="tab-icon">💰</span>
                                <span>حسب الفاتورة</span>
                            </button>
                        </div>

                        <form class="tool-form" id="systemSizingForm">
                            <!-- تبويب الاستهلاك -->
                            <div class="tab-content active" id="consumption-tab">
                                <div class="input-group">
                                    <label class="input-label">
                                        <span class="label-text">الاستهلاك الشهري (kWh)</span>
                                        <span class="label-hint">متوسط استهلاكك الشهري من الكهرباء</span>
                                        <input type="number" min="10" step="1" value="700" id="monthlyConsumption" class="input-field" required>
                                    </label>
                                </div>
                            </div>

                            <!-- تبويب الفاتورة -->
                            <div class="tab-content" id="bill-tab">
                                <div class="input-group">
                                    <label class="input-label">
                                        <span class="label-text">قيمة الفاتورة الشهرية (دينار)</span>
                                        <span class="label-hint">متوسط قيمة فاتورة الكهرباء الشهرية</span>
                                        <input type="number" min="5" step="0.5" value="85" id="monthlyBill" class="input-field">
                                    </label>
                                </div>
                                <div class="input-group">
                                    <label class="input-label">
                                        <span class="label-text">تعرفة الكهرباء (دينار/kWh)</span>
                                        <span class="label-hint">سعر الكيلوواط ساعة في فاتورتك</span>
                                        <input type="number" min="0.01" step="0.01" value="0.12" id="billKwhPrice" class="input-field">
                                    </label>
                                </div>
                            </div>

                            <!-- الموقع الجغرافي -->
                            <div class="input-group">
                                <label class="input-label">
                                    <span class="label-text">الموقع الجغرافي</span>
                                    <span class="label-hint">اختر أقرب مدينة لموقع التركيب</span>
                                    <select id="location" class="input-field" required>
                                        <option value="">اختر المدينة...</option>
                                        <option value="amman" data-sun="5.5">عمان (5.5 ساعة ذروة)</option>
                                        <option value="zarqa" data-sun="5.6">الزرقاء (5.6 ساعة ذروة)</option>
                                        <option value="irbid" data-sun="5.4">إربد (5.4 ساعة ذروة)</option>
                                        <option value="aqaba" data-sun="6.0">العقبة (6.0 ساعة ذروة)</option>
                                    </select>
                                </label>
                            </div>

                            <!-- المساحة المتاحة -->
                            <div class="input-group">
                                <label class="input-label">
                                    <span class="label-text">المساحة المتاحة (م²)</span>
                                    <span class="label-hint">المساحة المتاحة لتركيب الألواح الشمسية</span>
                                    <input type="number" min="10" step="0.5" value="80" id="surfaceArea" class="input-field" required>
                                </label>
                            </div>

                            <!-- الإعدادات المتقدمة -->
                            <div class="advanced-settings">
                                <div class="advanced-toggle" id="advancedToggle">
                                    <span class="toggle-text">⚙️ إعدادات فنية ومالية متقدمة</span>
                                    <span class="toggle-icon">▼</span>
                                </div>

                                <div class="advanced-content" id="advancedContent">
                                    <div class="advanced-grid">
                                        <div class="input-group">
                                            <label class="input-label">
                                                <span class="label-text">قدرة اللوح (واط)</span>
                                                <input type="number" min="200" max="700" step="10" value="550" id="panelWatt" class="input-field">
                                            </label>
                                        </div>

                                        <div class="input-group">
                                            <label class="input-label">
                                                <span class="label-text">خسائر النظام (%)</span>
                                                <input type="number" min="5" max="30" step="1" value="15" id="systemLoss" class="input-field">
                                            </label>
                                        </div>

                                        <div class="input-group">
                                            <label class="input-label">
                                                <span class="label-text">تكلفة الواط (دينار)</span>
                                                <input type="number" min="0.3" max="1.5" step="0.01" value="0.6" id="costPerWatt" class="input-field">
                                            </label>
                                        </div>

                                        <div class="input-group">
                                            <label class="input-label">
                                                <span class="label-text">تعرفة الكهرباء (دينار/kWh)</span>
                                                <input type="number" min="0.05" max="0.5" step="0.01" value="0.12" id="pricePerKwh" class="input-field">
                                            </label>
                                        </div>

                                        <div class="input-group">
                                            <label class="input-label">
                                                <span class="label-text">تهالك الألواح السنوي (%)</span>
                                                <input type="number" min="0.3" max="1.0" step="0.1" value="0.5" id="degradationRate" class="input-field">
                                            </label>
                                        </div>

                                        <div class="input-group">
                                            <label class="input-label">
                                                <span class="label-text">زاوية الميل (درجة)</span>
                                                <input type="number" min="0" max="60" step="1" value="30" id="tiltAngle" class="input-field">
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <button type="submit" class="calculate-button">
                                <span class="button-icon">🚀</span>
                                <span class="button-text">احسب التصميم الأمثل</span>
                            </button>
                        </form>
                    </div>
                    <!-- قسم النتائج -->
                    <div class="system-sizing-card results-section">
                        <div class="results-empty" id="resultsEmpty">
                            <div class="empty-state">
                                <div class="empty-icon">📊</div>
                                <h3>جاهز للحساب</h3>
                                <p>أدخل بياناتك في النموذج، ثم اضغط على "احسب التصميم الأمثل" لتظهر النتائج التفصيلية والتحليل المالي الشامل.</p>
                            </div>
                        </div>

                        <div class="results-content hidden" id="resultsContent">
                            <!-- ملخص النظام -->
                            <div class="results-header">
                                <h3>📋 ملخص النظام الأمثل</h3>
                                <div class="system-status" id="systemStatus">
                                    <span class="status-indicator"></span>
                                    <span class="status-text">نظام محسّن للاستهلاك</span>
                                </div>
                            </div>

                            <!-- البطاقات الرئيسية -->
                            <div class="summary-cards">
                                <div class="summary-card primary">
                                    <div class="card-icon">⚡</div>
                                    <div class="card-content">
                                        <div class="card-value" id="systemPower">--</div>
                                        <div class="card-label">قدرة النظام (kWp)</div>
                                        <div class="card-change" id="powerChange">--</div>
                                    </div>
                                </div>

                                <div class="summary-card">
                                    <div class="card-icon">🔆</div>
                                    <div class="card-content">
                                        <div class="card-value" id="panelCount">--</div>
                                        <div class="card-label">عدد الألواح</div>
                                        <div class="card-change" id="panelChange">--</div>
                                    </div>
                                </div>

                                <div class="summary-card">
                                    <div class="card-icon">🔄</div>
                                    <div class="card-content">
                                        <div class="card-value" id="inverterSize">--</div>
                                        <div class="card-label">قدرة العاكس (kW)</div>
                                        <div class="card-change" id="inverterChange">--</div>
                                    </div>
                                </div>

                                <div class="summary-card">
                                    <div class="card-icon">📐</div>
                                    <div class="card-content">
                                        <div class="card-value" id="requiredArea">--</div>
                                        <div class="card-label">المساحة المطلوبة (م²)</div>
                                        <div class="card-change" id="areaChange">--</div>
                                    </div>
                                </div>
                            </div>

                            <!-- تبويبات النتائج التفصيلية -->
                            <div class="results-tabs">
                                <button class="results-tab-button active" data-tab="technical">
                                    <span class="tab-icon">🔧</span>
                                    <span>التحليل الفني</span>
                                </button>
                                <button class="results-tab-button" data-tab="financial">
                                    <span class="tab-icon">💰</span>
                                    <span>التحليل المالي</span>
                                </button>
                                <button class="results-tab-button" data-tab="production">
                                    <span class="tab-icon">📈</span>
                                    <span>الإنتاج المتوقع</span>
                                </button>
                                <button class="results-tab-button" data-tab="recommendations">
                                    <span class="tab-icon">💡</span>
                                    <span>التوصيات</span>
                                </button>
                            </div>
                            <!-- محتوى التبويبات -->
                            <div class="results-tab-content">
                                <!-- التحليل الفني -->
                                <div class="tab-panel active" id="technical-panel">
                                    <div class="panel-section">
                                        <h4>🔧 التوزيع الكهربائي</h4>
                                        <div class="technical-grid">
                                            <div class="tech-item">
                                                <span class="tech-label">عدد السلاسل</span>
                                                <span class="tech-value" id="stringCount">--</span>
                                            </div>
                                            <div class="tech-item">
                                                <span class="tech-label">ألواح/سلسلة</span>
                                                <span class="tech-value" id="panelsPerString">--</span>
                                            </div>
                                            <div class="tech-item">
                                                <span class="tech-label">جهد النظام</span>
                                                <span class="tech-value" id="systemVoltage">--</span>
                                            </div>
                                            <div class="tech-item">
                                                <span class="tech-label">تيار النظام</span>
                                                <span class="tech-value" id="systemCurrent">--</span>
                                            </div>
                                            <div class="tech-item">
                                                <span class="tech-label">نسبة DC/AC</span>
                                                <span class="tech-value" id="dcAcRatio">--</span>
                                            </div>
                                            <div class="tech-item">
                                                <span class="tech-label">العامل المحدد</span>
                                                <span class="tech-value" id="limitingFactor">--</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="panel-section">
                                        <h4>📐 المواصفات الميكانيكية</h4>
                                        <div class="mechanical-specs">
                                            <div class="spec-item">
                                                <span class="spec-icon">📏</span>
                                                <div class="spec-content">
                                                    <span class="spec-label">أبعاد اللوح الواحد</span>
                                                    <span class="spec-value">2.28 × 1.13 متر</span>
                                                </div>
                                            </div>
                                            <div class="spec-item">
                                                <span class="spec-icon">⚖️</span>
                                                <div class="spec-content">
                                                    <span class="spec-label">وزن النظام الإجمالي</span>
                                                    <span class="spec-value" id="totalWeight">--</span>
                                                </div>
                                            </div>
                                            <div class="spec-item">
                                                <span class="spec-icon">🧭</span>
                                                <div class="spec-content">
                                                    <span class="spec-label">الاتجاه الأمثل</span>
                                                    <span class="spec-value">جنوب (180°)</span>
                                                </div>
                                            </div>
                                            <div class="spec-item">
                                                <span class="spec-icon">📐</span>
                                                <div class="spec-content">
                                                    <span class="spec-label">زاوية الميل المثلى</span>
                                                    <span class="spec-value" id="optimalTilt">--</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- التحليل المالي -->
                                <div class="tab-panel" id="financial-panel">
                                    <div class="panel-section">
                                        <h4>💰 التكاليف والعوائد</h4>
                                        <div class="financial-summary">
                                            <div class="financial-card cost">
                                                <div class="card-header">
                                                    <span class="card-icon">💸</span>
                                                    <span class="card-title">التكلفة الإجمالية</span>
                                                </div>
                                                <div class="card-value" id="totalCost">--</div>
                                                <div class="card-subtitle">دينار أردني</div>
                                            </div>

                                            <div class="financial-card savings">
                                                <div class="card-header">
                                                    <span class="card-icon">💚</span>
                                                    <span class="card-title">التوفير السنوي</span>
                                                </div>
                                                <div class="card-value" id="annualSavings">--</div>
                                                <div class="card-subtitle">دينار / سنة</div>
                                            </div>

                                            <div class="financial-card payback">
                                                <div class="card-header">
                                                    <span class="card-icon">⏰</span>
                                                    <span class="card-title">فترة الاسترداد</span>
                                                </div>
                                                <div class="card-value" id="paybackPeriod">--</div>
                                                <div class="card-subtitle">سنوات</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="panel-section">
                                        <h4>📊 تحليل الجدوى على 25 سنة</h4>
                                        <div class="roi-analysis">
                                            <div class="roi-item">
                                                <span class="roi-label">إجمالي الإنتاج</span>
                                                <span class="roi-value" id="totalProduction25">--</span>
                                                <span class="roi-unit">kWh</span>
                                            </div>
                                            <div class="roi-item">
                                                <span class="roi-label">إجمالي التوفير</span>
                                                <span class="roi-value" id="totalSavings25">--</span>
                                                <span class="roi-unit">دينار</span>
                                            </div>
                                            <div class="roi-item">
                                                <span class="roi-label">صافي الربح</span>
                                                <span class="roi-value" id="netProfit25">--</span>
                                                <span class="roi-unit">دينار</span>
                                            </div>
                                            <div class="roi-item">
                                                <span class="roi-label">معدل العائد</span>
                                                <span class="roi-value" id="roi25">--</span>
                                                <span class="roi-unit">%</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="panel-section">
                                        <h4>📈 تحليل الحساسية</h4>
                                        <div id="sensitivityAnalysis">
                                            <!-- سيتم ملؤها بواسطة JavaScript -->
                                        </div>
                                    </div>
                                </div>

                                <!-- الإنتاج المتوقع -->
                                <div class="tab-panel" id="production-panel">
                                    <div class="panel-section">
                                        <h4>📈 إنتاج الطاقة المتوقع</h4>
                                        <div class="production-cards">
                                            <div class="production-card">
                                                <div class="production-icon">☀️</div>
                                                <div class="production-content">
                                                    <div class="production-value" id="dailyProduction">--</div>
                                                    <div class="production-label">kWh / يوم</div>
                                                    <div class="production-subtitle">الإنتاج اليومي</div>
                                                </div>
                                            </div>

                                            <div class="production-card">
                                                <div class="production-icon">📅</div>
                                                <div class="production-content">
                                                    <div class="production-value" id="monthlyProduction">--</div>
                                                    <div class="production-label">kWh / شهر</div>
                                                    <div class="production-subtitle">الإنتاج الشهري</div>
                                                </div>
                                            </div>

                                            <div class="production-card">
                                                <div class="production-icon">🗓️</div>
                                                <div class="production-content">
                                                    <div class="production-value" id="yearlyProduction">--</div>
                                                    <div class="production-label">kWh / سنة</div>
                                                    <div class="production-subtitle">الإنتاج السنوي</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="panel-section">
                                        <h4>⚡ مؤشرات الأداء</h4>
                                        <div class="performance-metrics">
                                            <div class="metric-item">
                                                <span class="metric-label">عامل الاستخدام</span>
                                                <div class="metric-bar">
                                                    <div class="metric-fill" id="capacityFactorBar"></div>
                                                </div>
                                                <span class="metric-value" id="capacityFactor">--</span>
                                            </div>

                                            <div class="metric-item">
                                                <span class="metric-label">كفاءة النظام</span>
                                                <div class="metric-bar">
                                                    <div class="metric-fill" id="systemEfficiencyBar"></div>
                                                </div>
                                                <span class="metric-value" id="systemEfficiencyDisplay">--</span>
                                            </div>

                                            <div class="metric-item">
                                                <span class="metric-label">تغطية الاستهلاك</span>
                                                <div class="metric-bar">
                                                    <div class="metric-fill" id="consumptionCoverageBar"></div>
                                                </div>
                                                <span class="metric-value" id="consumptionCoverage">--</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- التوصيات -->
                                <div class="tab-panel" id="recommendations-panel">
                                    <div class="panel-section">
                                        <h4>💡 التوصيات الذكية</h4>
                                        <div class="recommendations-list" id="recommendationsList">
                                            <!-- سيتم ملؤها بواسطة JavaScript -->
                                        </div>
                                    </div>

                                    <div class="panel-section">
                                        <div id="systemComparison">
                                            <!-- سيتم ملؤها بواسطة JavaScript -->
                                        </div>
                                    </div>

                                    <div class="panel-section">
                                        <h4>⚠️ تنبيهات مهمة</h4>
                                        <div class="alerts-list" id="alertsList">
                                            <!-- سيتم ملؤها بواسطة JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            const form = container.querySelector('#systemSizingForm');
            const resultsWrapper = container.querySelector('#systemSizingResults');
            const emptyState = container.querySelector('#systemSizingEmpty');

            const getEl = (id) => container.querySelector('#' + id);

            function setValue(id, text) {
                const el = getEl(id);
                if (el) el.textContent = text;
            }

            function formatYears(value) {
                if (!Number.isFinite(value) || value <= 0) return 'غير متاح';
                if (value >= 25) return 'أكثر من 25 سنة';
                const years = Math.floor(value);
                const months = Math.round((value - years) * 12);
                if (years === 0) return `${months} شهر`;
                if (months === 0) return `${years} سنة`;
                return `${years} سنة و ${months} شهر`;
            }

            // Advanced System Calculator Logic
            const calculator = {
                // Location-specific data
                locationData: {
                    amman: { sunHours: 5.5, latitude: 31.95, irradiance: 2000 },
                    zarqa: { sunHours: 5.6, latitude: 32.07, irradiance: 2050 },
                    irbid: { sunHours: 5.4, latitude: 32.56, irradiance: 1950 },
                    aqaba: { sunHours: 6.0, latitude: 29.53, irradiance: 2200 }
                },

                // Panel specifications
                panelSpecs: {
                    width: 2.28,
                    height: 1.13,
                    area: 2.58,
                    weight: 28,
                    spacingFactor: 1.5
                },

                // Calculate optimal system size
                calculateSystemSize(input) {
                    const location = this.locationData[input.location];
                    const dailyConsumption = input.calculationMode === 'consumption'
                        ? input.monthlyConsumption / 30
                        : (input.monthlyBill / input.kwhPrice) / 30;

                    // System losses calculation
                    const systemLossFactor = (100 - input.systemLoss) / 100;
                    const temperatureLoss = 0.04; // 4% temperature loss
                    const shadingLoss = 0.02; // 2% shading loss
                    const totalEfficiency = systemLossFactor * (1 - temperatureLoss) * (1 - shadingLoss);

                    // Required system size based on consumption
                    const consumptionBasedSize = dailyConsumption / (location.sunHours * totalEfficiency);

                    // Maximum system size based on available area
                    const panelAreaWithSpacing = this.panelSpecs.area * this.panelSpecs.spacingFactor;
                    const maxPanelsFromArea = Math.floor(input.surfaceArea / panelAreaWithSpacing);
                    const areaBasedSize = (maxPanelsFromArea * input.panelWattage) / 1000;

                    // Determine limiting factor
                    const limitingFactor = consumptionBasedSize <= areaBasedSize ? 'consumption' : 'area';
                    const optimizedSystemSize = Math.min(consumptionBasedSize, areaBasedSize);

                    return {
                        systemSize: optimizedSystemSize,
                        limitingFactor,
                        dailyConsumption,
                        totalEfficiency,
                        maxPanelsFromArea,
                        consumptionBasedSize,
                        areaBasedSize
                    };
                },

                // Calculate panel configuration
                calculatePanelConfig(systemSize, panelWattage, surfaceArea) {
                    const panelCount = Math.ceil((systemSize * 1000) / panelWattage);
                    const requiredArea = panelCount * this.panelSpecs.area * this.panelSpecs.spacingFactor;
                    const totalWeight = panelCount * this.panelSpecs.weight;

                    // String configuration
                    const maxPanelsPerString = 20; // Typical MPPT limit
                    const minPanelsPerString = 8;   // Minimum for efficiency

                    let panelsPerString = Math.min(maxPanelsPerString, Math.max(minPanelsPerString,
                        Math.floor(Math.sqrt(panelCount))));

                    const stringCount = Math.ceil(panelCount / panelsPerString);
                    const lastStringPanels = panelCount - ((stringCount - 1) * panelsPerString);

                    // Electrical calculations
                    const panelVoltage = 45; // Typical Vmp
                    const panelCurrent = panelWattage / panelVoltage;
                    const stringVoltage = panelsPerString * panelVoltage;
                    const systemCurrent = stringCount * panelCurrent;

                    return {
                        panelCount,
                        requiredArea,
                        totalWeight,
                        panelsPerString,
                        stringCount,
                        lastStringPanels,
                        stringVoltage,
                        systemCurrent,
                        dcPower: systemSize
                    };
                },

                // Calculate inverter requirements
                calculateInverterConfig(systemSize, panelConfig) {
                    const dcAcRatio = 1.2; // Typical oversizing
                    const inverterSize = systemSize / dcAcRatio;

                    // Determine phase type
                    const phase = inverterSize > 10 ? 'Three-Phase' : 'Single-Phase';

                    // MPPT voltage range
                    const mpptVoltageMin = Math.max(300, panelConfig.stringVoltage * 0.8);
                    const mpptVoltageMax = Math.min(800, panelConfig.stringVoltage * 1.2);

                    return {
                        size: inverterSize,
                        phase,
                        mpptVoltage: `${Math.round(mpptVoltageMin)}-${Math.round(mpptVoltageMax)}V`,
                        dcAcRatio: dcAcRatio.toFixed(2)
                    };
                },

                // Calculate production estimates
                calculateProduction(systemSize, location, tiltAngle = 30) {
                    const locationData = this.locationData[location];

                    // Tilt factor calculation
                    const optimalTilt = locationData.latitude;
                    const tiltFactor = 1 - Math.abs(tiltAngle - optimalTilt) * 0.002;

                    // Monthly production variation (simplified)
                    const monthlyFactors = [0.7, 0.8, 0.9, 1.0, 1.1, 1.2, 1.2, 1.1, 1.0, 0.9, 0.8, 0.7];

                    const dailyProduction = systemSize * locationData.sunHours * tiltFactor;
                    const monthlyProduction = dailyProduction * 30;
                    const yearlyProduction = dailyProduction * 365;

                    // Capacity factor
                    const capacityFactor = (dailyProduction / (systemSize * 24)) * 100;

                    return {
                        daily: dailyProduction,
                        monthly: monthlyProduction,
                        yearly: yearlyProduction,
                        capacityFactor,
                        monthlyBreakdown: monthlyFactors.map((factor, index) => ({
                            month: index + 1,
                            production: monthlyProduction * factor,
                            factor
                        }))
                    };
                },

                // Advanced financial analysis with sensitivity
                calculateAdvancedFinancials(systemSize, input, production) {
                    const totalCost = systemSize * 1000 * input.costPerWatt;
                    const annualSavings = production.yearly * input.kwhPrice;
                    const paybackYears = totalCost / annualSavings;

                    // 25-year analysis with degradation
                    let totalProduction25 = 0;
                    let totalSavings25 = 0;

                    for (let year = 1; year <= 25; year++) {
                        const degradationFactor = Math.pow(1 - input.degradationRate/100, year - 1);
                        const yearlyProduction = production.yearly * degradationFactor;
                        totalProduction25 += yearlyProduction;
                        totalSavings25 += yearlyProduction * input.kwhPrice;
                    }

                    const netProfit25 = totalSavings25 - totalCost;
                    const roi25 = (netProfit25 / totalCost) * 100;

                    // Sensitivity analysis
                    const sensitivityAnalysis = this.performSensitivityAnalysis(systemSize, input, production);

                    return {
                        totalCost,
                        annualSavings,
                        paybackYears,
                        totalProduction25,
                        totalSavings25,
                        netProfit25,
                        roi25,
                        sensitivityAnalysis
                    };
                },

                // Sensitivity analysis for key parameters
                performSensitivityAnalysis(systemSize, input, production) {
                    const baseCase = {
                        totalCost: systemSize * 1000 * input.costPerWatt,
                        annualSavings: production.yearly * input.kwhPrice
                    };

                    const scenarios = {
                        costOptimistic: this.calculateScenario(systemSize, {...input, costPerWatt: input.costPerWatt * 0.9}, production),
                        costPessimistic: this.calculateScenario(systemSize, {...input, costPerWatt: input.costPerWatt * 1.1}, production),
                        priceOptimistic: this.calculateScenario(systemSize, {...input, kwhPrice: input.kwhPrice * 1.1}, production),
                        pricePessimistic: this.calculateScenario(systemSize, {...input, kwhPrice: input.kwhPrice * 0.9}, production),
                        productionOptimistic: this.calculateScenario(systemSize, input, {...production, yearly: production.yearly * 1.1}),
                        productionPessimistic: this.calculateScenario(systemSize, input, {...production, yearly: production.yearly * 0.9})
                    };

                    return {
                        baseCase,
                        scenarios,
                        bestCase: Math.min(...Object.values(scenarios).map(s => s.paybackYears)),
                        worstCase: Math.max(...Object.values(scenarios).map(s => s.paybackYears))
                    };
                },

                calculateScenario(systemSize, input, production) {
                    const totalCost = systemSize * 1000 * input.costPerWatt;
                    const annualSavings = production.yearly * input.kwhPrice;
                    const paybackYears = totalCost / annualSavings;

                    return { totalCost, annualSavings, paybackYears };
                },

                // Smart recommendations engine
                generateSmartRecommendations(results) {
                    const recommendations = [];
                    const { systemCalc, panelConfig, input, production, financials } = results;

                    // Performance optimization recommendations
                    if (production.capacityFactor < 18) {
                        recommendations.push({
                            type: 'performance',
                            priority: 'high',
                            icon: '⚡',
                            title: 'تحسين الأداء',
                            description: 'عامل الاستخدام منخفض. يُنصح بمراجعة زاوية الميل والاتجاه.',
                            impact: 'زيادة الإنتاج بنسبة 5-15%'
                        });
                    }

                    // Financial optimization recommendations
                    if (financials.paybackYears > 8) {
                        recommendations.push({
                            type: 'financial',
                            priority: 'medium',
                            icon: '💰',
                            title: 'تحسين الجدوى المالية',
                            description: 'فترة الاسترداد طويلة. فكر في تقليل التكلفة أو زيادة حجم النظام.',
                            impact: 'تقليل فترة الاسترداد بـ 1-3 سنوات'
                        });
                    }

                    // System sizing recommendations
                    if (systemCalc.limitingFactor === 'area' && systemCalc.consumptionBasedSize > systemCalc.areaBasedSize * 1.2) {
                        recommendations.push({
                            type: 'sizing',
                            priority: 'medium',
                            icon: '📏',
                            title: 'توسيع المساحة',
                            description: 'يمكن تحسين تغطية الاستهلاك بزيادة المساحة المتاحة.',
                            impact: `زيادة قدرة النظام إلى ${systemCalc.consumptionBasedSize.toFixed(1)} كيلوواط`
                        });
                    }

                    // Technology recommendations
                    if (input.panelWattage < 500) {
                        recommendations.push({
                            type: 'technology',
                            priority: 'low',
                            icon: '🔬',
                            title: 'تحديث التكنولوجيا',
                            description: 'استخدام ألواح عالية الكفاءة يقلل المساحة المطلوبة.',
                            impact: 'توفير 10-20% من المساحة'
                        });
                    }

                    return recommendations.sort((a, b) => {
                        const priorityOrder = { high: 3, medium: 2, low: 1 };
                        return priorityOrder[b.priority] - priorityOrder[a.priority];
                    });
                },

                // System comparison feature
                compareSystemOptions(baseInput) {
                    const options = [
                        { name: 'النظام الحالي', input: baseInput },
                        { name: 'نظام محسّن (+20% قدرة)', input: {...baseInput, panelWattage: baseInput.panelWattage * 1.2} },
                        { name: 'نظام اقتصادي (-15% تكلفة)', input: {...baseInput, costPerWatt: baseInput.costPerWatt * 0.85} },
                        { name: 'نظام عالي الكفاءة', input: {...baseInput, panelWattage: 600, systemLoss: 10} }
                    ];

                    return options.map(option => {
                        const systemCalc = this.calculateSystemSize(option.input);
                        const panelConfig = this.calculatePanelConfig(systemCalc.systemSize, option.input.panelWattage, option.input.surfaceArea);
                        const production = this.calculateProduction(systemCalc.systemSize, option.input.location, option.input.tiltAngle);
                        const financials = this.calculateAdvancedFinancials(systemCalc.systemSize, option.input, production);

                        return {
                            name: option.name,
                            systemSize: systemCalc.systemSize,
                            panelCount: panelConfig.panelCount,
                            totalCost: financials.totalCost,
                            paybackYears: financials.paybackYears,
                            roi25: financials.roi25,
                            yearlyProduction: production.yearly
                        };
                    });
                }
            };

            form.addEventListener('submit', function (event) {
                event.preventDefault();

                // Get form data
                const formData = new FormData(form);
                const input = {
                    calculationMode: document.querySelector('.tab-button.active').dataset.tab,
                    monthlyConsumption: Number(document.getElementById('monthlyConsumption').value),
                    monthlyBill: Number(document.getElementById('monthlyBill').value),
                    surfaceArea: Number(document.getElementById('surfaceArea').value),
                    location: document.getElementById('location').value,
                    panelWattage: Number(document.getElementById('panelWatt').value),
                    systemLoss: Number(document.getElementById('systemLoss').value),
                    costPerWatt: Number(document.getElementById('costPerWatt').value),
                    kwhPrice: Number(document.getElementById('pricePerKwh').value) || Number(document.getElementById('billKwhPrice').value),
                    degradationRate: Number(document.getElementById('degradationRate').value),
                    tiltAngle: Number(document.getElementById('tiltAngle').value)
                };

                // Validate inputs
                if (!input.location || !input.surfaceArea || !input.panelWattage) {
                    showError('الرجاء تعبئة جميع الحقول المطلوبة');
                    return;
                }

                if (input.calculationMode === 'consumption' && !input.monthlyConsumption) {
                    showError('الرجاء إدخال الاستهلاك الشهري');
                    return;
                }

                if (input.calculationMode === 'bill' && (!input.monthlyBill || !input.kwhPrice)) {
                    showError('الرجاء إدخال قيمة الفاتورة وسعر الكيلوواط ساعة');
                    return;
                }

                // Calculate system design
                try {
                    const systemCalc = calculator.calculateSystemSize(input);
                    const panelConfig = calculator.calculatePanelConfig(systemCalc.systemSize, input.panelWattage, input.surfaceArea);
                    const inverterConfig = calculator.calculateInverterConfig(systemCalc.systemSize, panelConfig);
                    const production = calculator.calculateProduction(systemCalc.systemSize, input.location, input.tiltAngle);
                    const financials = calculator.calculateAdvancedFinancials(systemCalc.systemSize, input, production);
                    const smartRecommendations = calculator.generateSmartRecommendations({
                        systemCalc, panelConfig, input, production, financials
                    });
                    const systemComparison = calculator.compareSystemOptions(input);

                    // Display results
                    displayResults({
                        systemCalc,
                        panelConfig,
                        inverterConfig,
                        production,
                        financials,
                        smartRecommendations,
                        systemComparison,
                        input
                    });

                    // Show success message
                    showSuccess('تم حساب النظام بنجاح! راجع النتائج والتوصيات.');

                } catch (error) {
                    console.error('Calculation error:', error);
                    showError('حدث خطأ في الحسابات. الرجاء المحاولة مرة أخرى.');
                }
            });

            // Helper functions
            function showError(message) {
                const emptyState = container.querySelector('#resultsEmpty');
                const resultsContent = container.querySelector('#resultsContent');

                emptyState.querySelector('.empty-state p').textContent = message;
                emptyState.classList.remove('hidden');
                resultsContent.classList.add('hidden');
            }

            function displayResults(results) {
                const emptyState = container.querySelector('#resultsEmpty');
                const resultsContent = container.querySelector('#resultsContent');

                // Hide empty state and show results
                emptyState.classList.add('hidden');
                resultsContent.classList.remove('hidden');

                // Update summary cards
                updateSummaryCards(results);

                // Update detailed tabs
                updateTechnicalTab(results);
                updateFinancialTab(results);
                updateProductionTab(results);
                updateRecommendationsTab(results);
            }

            function updateSummaryCards(results) {
                const { systemCalc, panelConfig, inverterConfig, production } = results;

                // System power
                container.querySelector('#systemPower').textContent = systemCalc.systemSize.toFixed(1);

                // Panel count
                container.querySelector('#panelCount').textContent = panelConfig.panelCount;

                // Inverter size
                container.querySelector('#inverterSize').textContent = inverterConfig.size.toFixed(1);

                // Required area
                container.querySelector('#requiredArea').textContent = panelConfig.requiredArea.toFixed(1);

                // System status
                const statusText = systemCalc.limitingFactor === 'consumption'
                    ? 'نظام محسّن للاستهلاك'
                    : 'نظام محدود بالمساحة';
                container.querySelector('.status-text').textContent = statusText;
            }

            function updateTechnicalTab(results) {
                const { panelConfig, inverterConfig } = results;

                // Technical grid
                container.querySelector('#stringCount').textContent = panelConfig.stringCount;
                container.querySelector('#panelsPerString').textContent = panelConfig.panelsPerString;
                container.querySelector('#systemVoltage').textContent = `${panelConfig.stringVoltage.toFixed(0)}V`;
                container.querySelector('#systemCurrent').textContent = `${panelConfig.systemCurrent.toFixed(1)}A`;
                container.querySelector('#dcAcRatio').textContent = inverterConfig.dcAcRatio;
                container.querySelector('#limitingFactor').textContent =
                    results.systemCalc.limitingFactor === 'consumption' ? 'الاستهلاك' : 'المساحة المتاحة';

                // Mechanical specs
                container.querySelector('#totalWeight').textContent = `${panelConfig.totalWeight} كغ`;
                container.querySelector('#optimalTilt').textContent = `${results.input.tiltAngle}°`;
            }

            function updateFinancialTab(results) {
                const { financials } = results;

                // Update financial cards
                container.querySelector('#totalCost').textContent = financials.totalCost.toLocaleString('ar-JO');
                container.querySelector('#annualSavings').textContent = financials.annualSavings.toLocaleString('ar-JO');
                container.querySelector('#paybackPeriod').textContent = financials.paybackYears.toFixed(1);

                // 25-year analysis
                container.querySelector('#totalProduction25').textContent = financials.totalProduction25.toLocaleString('ar-JO');
                container.querySelector('#totalSavings25').textContent = financials.totalSavings25.toLocaleString('ar-JO');
                container.querySelector('#netProfit25').textContent = financials.netProfit25.toLocaleString('ar-JO');
                container.querySelector('#roi25').textContent = financials.roi25.toFixed(1);

                // Add sensitivity analysis display
                displaySensitivityAnalysis(financials.sensitivityAnalysis);
            }

            function displaySensitivityAnalysis(sensitivity) {
                const sensitivityContainer = container.querySelector('#sensitivityAnalysis');
                if (!sensitivityContainer) return;

                const { bestCase, worstCase, scenarios } = sensitivity;

                sensitivityContainer.innerHTML = `
                    <div class="sensitivity-summary">
                        <div class="sensitivity-item">
                            <span class="sensitivity-label">أفضل سيناريو</span>
                            <span class="sensitivity-value best">${bestCase.toFixed(1)} سنة</span>
                        </div>
                        <div class="sensitivity-item">
                            <span class="sensitivity-label">أسوأ سيناريو</span>
                            <span class="sensitivity-value worst">${worstCase.toFixed(1)} سنة</span>
                        </div>
                    </div>
                    <div class="sensitivity-details">
                        <div class="sensitivity-scenario">
                            <span class="scenario-label">تحسن التكلفة (-10%)</span>
                            <span class="scenario-value">${scenarios.costOptimistic.paybackYears.toFixed(1)} سنة</span>
                        </div>
                        <div class="sensitivity-scenario">
                            <span class="scenario-label">ارتفاع التكلفة (+10%)</span>
                            <span class="scenario-value">${scenarios.costPessimistic.paybackYears.toFixed(1)} سنة</span>
                        </div>
                        <div class="sensitivity-scenario">
                            <span class="scenario-label">ارتفاع أسعار الكهرباء (+10%)</span>
                            <span class="scenario-value">${scenarios.priceOptimistic.paybackYears.toFixed(1)} سنة</span>
                        </div>
                        <div class="sensitivity-scenario">
                            <span class="scenario-label">انخفاض أسعار الكهرباء (-10%)</span>
                            <span class="scenario-value">${scenarios.pricePessimistic.paybackYears.toFixed(1)} سنة</span>
                        </div>
                    </div>
                `;
            }

            function updateProductionTab(results) {
                const { production, systemCalc } = results;

                // Production cards
                container.querySelector('#dailyProduction').textContent = production.daily.toFixed(1);
                container.querySelector('#monthlyProduction').textContent = production.monthly.toFixed(0);
                container.querySelector('#yearlyProduction').textContent = production.yearly.toFixed(0);

                // Performance metrics
                container.querySelector('#capacityFactor').textContent = `${production.capacityFactor.toFixed(1)}%`;
                container.querySelector('#systemEfficiencyDisplay').textContent = `${(systemCalc.totalEfficiency * 100).toFixed(1)}%`;

                // Consumption coverage
                const coverage = Math.min(100, (production.monthly / (systemCalc.dailyConsumption * 30)) * 100);
                container.querySelector('#consumptionCoverage').textContent = `${coverage.toFixed(1)}%`;

                // Update progress bars
                updateProgressBar('#capacityFactorBar', production.capacityFactor);
                updateProgressBar('#systemEfficiencyBar', systemCalc.totalEfficiency * 100);
                updateProgressBar('#consumptionCoverageBar', coverage);
            }

            function updateProgressBar(selector, value) {
                const bar = container.querySelector(selector);
                if (bar) {
                    bar.style.width = `${Math.min(100, value)}%`;
                }
            }

            function updateRecommendationsTab(results) {
                const { smartRecommendations, systemComparison } = results;
                const recommendationsList = container.querySelector('#recommendationsList');
                const alertsList = container.querySelector('#alertsList');

                // Display smart recommendations
                recommendationsList.innerHTML = smartRecommendations.map(rec => `
                    <div class="recommendation-item ${rec.priority}">
                        <div class="recommendation-icon">${rec.icon}</div>
                        <div class="recommendation-content">
                            <div class="recommendation-header">
                                <div class="recommendation-title">${rec.title}</div>
                                <div class="recommendation-priority">${getPriorityText(rec.priority)}</div>
                            </div>
                            <div class="recommendation-text">${rec.description}</div>
                            <div class="recommendation-impact">${rec.impact}</div>
                        </div>
                    </div>
                `).join('');

                // Display system comparison
                displaySystemComparison(systemComparison);

                // Generate and display alerts
                const alerts = generateAlerts(results);
                alertsList.innerHTML = alerts.map(alert => `
                    <div class="alert-item">
                        <div class="alert-icon">${alert.icon}</div>
                        <div class="alert-content">
                            <div class="alert-title">${alert.title}</div>
                            <div class="alert-text">${alert.text}</div>
                        </div>
                    </div>
                `).join('');
            }

            function getPriorityText(priority) {
                const priorities = {
                    high: 'أولوية عالية',
                    medium: 'أولوية متوسطة',
                    low: 'أولوية منخفضة'
                };
                return priorities[priority] || '';
            }

            function displaySystemComparison(comparison) {
                const comparisonContainer = container.querySelector('#systemComparison');
                if (!comparisonContainer) return;

                comparisonContainer.innerHTML = `
                    <h4>🔍 مقارنة خيارات النظام</h4>
                    <div class="comparison-table">
                        <div class="comparison-header">
                            <div class="comparison-cell">الخيار</div>
                            <div class="comparison-cell">القدرة (kWp)</div>
                            <div class="comparison-cell">التكلفة (دينار)</div>
                            <div class="comparison-cell">الاسترداد (سنة)</div>
                            <div class="comparison-cell">العائد 25 سنة (%)</div>
                        </div>
                        ${comparison.map((option, index) => `
                            <div class="comparison-row ${index === 0 ? 'current' : ''}">
                                <div class="comparison-cell">${option.name}</div>
                                <div class="comparison-cell">${option.systemSize.toFixed(1)}</div>
                                <div class="comparison-cell">${option.totalCost.toLocaleString('ar-JO')}</div>
                                <div class="comparison-cell">${option.paybackYears.toFixed(1)}</div>
                                <div class="comparison-cell">${option.roi25.toFixed(1)}%</div>
                            </div>
                        `).join('')}
                    </div>
                `;
            }

            function generateRecommendations(results) {
                const recommendations = [];
                const { systemCalc, panelConfig, input, production } = results;

                // Tilt angle recommendation
                const optimalTilt = calculator.locationData[input.location].latitude;
                if (Math.abs(input.tiltAngle - optimalTilt) > 5) {
                    recommendations.push({
                        icon: '📐',
                        title: 'تحسين زاوية الميل',
                        text: `للحصول على أفضل أداء، يُنصح بضبط زاوية الميل على ${optimalTilt}° بدلاً من ${input.tiltAngle}°`
                    });
                }

                // System sizing recommendation
                if (systemCalc.limitingFactor === 'area') {
                    recommendations.push({
                        icon: '📏',
                        title: 'توسيع المساحة',
                        text: `يمكن زيادة حجم النظام إلى ${systemCalc.consumptionBasedSize.toFixed(1)} كيلوواط إذا توفرت مساحة إضافية`
                    });
                }

                // Battery storage recommendation
                if (production.daily > systemCalc.dailyConsumption * 1.2) {
                    recommendations.push({
                        icon: '🔋',
                        title: 'إضافة تخزين البطاريات',
                        text: 'النظام ينتج طاقة فائضة، يُنصح بإضافة نظام تخزين للاستفادة من الفائض'
                    });
                }

                return recommendations;
            }

            function generateAlerts(results) {
                const alerts = [];
                const { systemCalc, panelConfig, input } = results;

                // Area constraint alert
                if (panelConfig.requiredArea > input.surfaceArea) {
                    alerts.push({
                        icon: '⚠️',
                        title: 'تحذير المساحة',
                        text: `المساحة المطلوبة (${panelConfig.requiredArea.toFixed(1)} م²) أكبر من المساحة المتاحة (${input.surfaceArea} م²)`
                    });
                }

                // High cost per watt alert
                if (input.costPerWatt > 0.8) {
                    alerts.push({
                        icon: '💰',
                        title: 'تكلفة مرتفعة',
                        text: 'تكلفة الواط مرتفعة نسبياً، يُنصح بمقارنة عروض أسعار متعددة'
                    });
                }

                return alerts;
            }

            // Event handlers for tabs and interactions
            function setupEventHandlers() {
                // Calculation mode tabs
                const tabButtons = container.querySelectorAll('.tab-button');
                const tabContents = container.querySelectorAll('.tab-content');

                tabButtons.forEach(button => {
                    button.addEventListener('click', () => {
                        const tabId = button.dataset.tab;

                        // Update active tab button
                        tabButtons.forEach(btn => btn.classList.remove('active'));
                        button.classList.add('active');

                        // Update active tab content
                        tabContents.forEach(content => {
                            content.classList.remove('active');
                            if (content.id === `${tabId}-tab`) {
                                content.classList.add('active');
                            }
                        });
                    });
                });

                // Results tabs
                const resultsTabButtons = container.querySelectorAll('.results-tab-button');
                const tabPanels = container.querySelectorAll('.tab-panel');

                resultsTabButtons.forEach(button => {
                    button.addEventListener('click', () => {
                        const tabId = button.dataset.tab;

                        // Update active results tab button
                        resultsTabButtons.forEach(btn => btn.classList.remove('active'));
                        button.classList.add('active');

                        // Update active tab panel
                        tabPanels.forEach(panel => {
                            panel.classList.remove('active');
                            if (panel.id === `${tabId}-panel`) {
                                panel.classList.add('active');
                            }
                        });
                    });
                });

                // Advanced settings toggle
                const advancedToggle = container.querySelector('#advancedToggle');
                const advancedContent = container.querySelector('#advancedContent');

                if (advancedToggle && advancedContent) {
                    advancedToggle.addEventListener('click', () => {
                        const isExpanded = advancedToggle.classList.contains('expanded');

                        if (isExpanded) {
                            advancedToggle.classList.remove('expanded');
                            advancedContent.classList.remove('expanded');
                        } else {
                            advancedToggle.classList.add('expanded');
                            advancedContent.classList.add('expanded');
                        }
                    });
                }

                // Location change handler
                const locationSelect = container.querySelector('#location');
                if (locationSelect) {
                    locationSelect.addEventListener('change', (e) => {
                        const selectedOption = e.target.selectedOptions[0];
                        const sunHours = selectedOption.dataset.sun;

                        // Update sun hours display if needed
                        console.log(`Selected location: ${e.target.value}, Sun hours: ${sunHours}`);
                    });
                }

                // Real-time input validation
                const inputs = container.querySelectorAll('.input-field');
                inputs.forEach(input => {
                    input.addEventListener('input', validateInput);
                    input.addEventListener('blur', validateInput);
                });
            }

            function validateInput(event) {
                const input = event.target;
                const value = parseFloat(input.value);

                // Remove previous validation classes
                input.classList.remove('invalid', 'valid');

                // Basic validation
                if (input.hasAttribute('required') && (!input.value || isNaN(value))) {
                    input.classList.add('invalid');
                    return false;
                }

                // Range validation
                const min = parseFloat(input.getAttribute('min'));
                const max = parseFloat(input.getAttribute('max'));

                if (!isNaN(min) && value < min) {
                    input.classList.add('invalid');
                    return false;
                }

                if (!isNaN(max) && value > max) {
                    input.classList.add('invalid');
                    return false;
                }

                input.classList.add('valid');
                return true;
            }

            // Initialize event handlers
            setupEventHandlers();

            // Error handling and user feedback functions
            function showError(message) {
                const errorContainer = container.querySelector('.error-message') || createErrorContainer();
                errorContainer.textContent = message;
                errorContainer.style.display = 'block';
                setTimeout(() => {
                    errorContainer.style.display = 'none';
                }, 5000);
            }

            function createErrorContainer() {
                const errorDiv = document.createElement('div');
                errorDiv.className = 'error-message';
                errorDiv.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: rgba(239, 68, 68, 0.9);
                    color: white;
                    padding: 1rem;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    z-index: 1000;
                    max-width: 300px;
                    display: none;
                `;
                document.body.appendChild(errorDiv);
                return errorDiv;
            }

            function showSuccess(message) {
                const successContainer = container.querySelector('.success-message') || createSuccessContainer();
                successContainer.textContent = message;
                successContainer.style.display = 'block';
                setTimeout(() => {
                    successContainer.style.display = 'none';
                }, 3000);
            }

            function createSuccessContainer() {
                const successDiv = document.createElement('div');
                successDiv.className = 'success-message';
                successDiv.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: rgba(34, 197, 94, 0.9);
                    color: white;
                    padding: 1rem;
                    border-radius: 8px;
                    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
                    z-index: 1000;
                    max-width: 300px;
                    display: none;
                `;
                document.body.appendChild(successDiv);
                return successDiv;
            }


        }

        function renderFieldInspector(container) {
            const checklist = [
                { id: 'site', title: 'مسح الموقع', description: 'تأكيد ثبات الأسطح ومراجعة أماكن التثبيت ونقاط الربط الأرضي.' },
                { id: 'structure', title: 'الهياكل والحمالات', description: 'التأكد من شد المسامير والعزل المائي حول قواعد التثبيت.' },
                { id: 'wiring', title: 'التوصيلات الكهربائية', description: 'مراجعة اتجاه الأسلاك، حماية القنوات، وإحكام الأطراف داخل العواكس.' },
                { id: 'testing', title: 'اختبارات التشغيل', description: 'قياس جهد الدارة المفتوحة، وفحص القاطع الرئيسي، وتوثيق القراءات.' },
                { id: 'handover', title: 'تسليم المشروع', description: 'التقاط صور نهائية، تسليم الملفات للعميل، وتأكيد خطة الصيانة.' }
            ];

            container.innerHTML = `
                <div class="tool-header">
                    <h2>المفتش الميداني</h2>
                    <p>تابع تقدم فريق التنفيذ عبر قائمة تحقق ذكية يتم مزامنتها مع لوحة القيادة فورًا.</p>
                </div>
                <div class="tool-progress">
                    <span id="fieldInspectorProgressLabel">التقدم: 0%</span>
                    <div class="tool-progress-bar"><div class="tool-progress-fill" id="fieldInspectorProgress"></div></div>
                </div>
                <div class="tool-checklist" id="fieldInspectorList"></div>
            `;

            const listContainer = container.querySelector('#fieldInspectorList');
            const progressFill = container.querySelector('#fieldInspectorProgress');
            const progressLabel = container.querySelector('#fieldInspectorProgressLabel');

            checklist.forEach(item => {
                const element = document.createElement('label');
                element.className = 'tool-checklist-item';
                element.innerHTML = `
                    <input type="checkbox" id="inspect-${item.id}">
                    <div>
                        <strong>${item.title}</strong>
                        <div class="tool-note">${item.description}</div>
                    </div>
                `;
                listContainer.appendChild(element);
            });

            function updateProgress() {
                const total = checklist.length;
                const completed = checklist.filter(item => {
                    const checkbox = document.getElementById(`inspect-${item.id}`);
                    return checkbox && checkbox.checked;
                }).length;
                const percent = Math.round((completed / total) * 100);
                progressFill.style.width = percent + '%';
                progressLabel.textContent = `التقدم: ${percent}%`;
            }

            listContainer.querySelectorAll('input[type="checkbox"]').forEach(input => {
                input.addEventListener('change', updateProgress);
            });

            updateProgress();
        }

        function renderStringConfiguration(container) {
            container.innerHTML = `
                <div class="tool-header">
                    <h2>تهيئة السلاسل</h2>
                    <p>احسب أفضل توزيع للألواح على السلاسل وفق حدود الجهد والتيار للعاكس مع هامش أمان للظروف الباردة.</p>
                </div>
                <form class="tool-form" id="stringConfigForm">
                    <label>أقصى جهد للعاكس (V)
                        <input type="number" min="200" max="1500" value="1000" id="inverterMaxVoltage" required>
                    </label>
                    <label>جهد الدارة المفتوحة للوحدة (Voc)
                        <input type="number" min="20" max="80" value="49" id="panelVoc" required>
                    </label>
                    <label>أقل درجة حرارة متوقعة في الموقع (°C)
                        <input type="number" min="-20" max="25" value="0" id="lowestTemperature" required>
                    </label>
                    <label>قدرة النظام المستهدفة (kW)
                        <input type="number" min="1" step="0.1" value="120" id="targetSystemPower" required>
                    </label>
                    <label>قدرة اللوح الواحد (W)
                        <input type="number" min="200" max="700" value="550" id="stringPanelWatt" required>
                    </label>
                    <button type="submit" class="tool-button">احسب السلاسل</button>
                </form>
                <div class="tool-output" id="stringConfigResult">أدخل البيانات لعرض توصية التوصيل.</div>
            `;

            const form = container.querySelector('#stringConfigForm');
            const result = container.querySelector('#stringConfigResult');

            form.addEventListener('submit', function (event) {
                event.preventDefault();
                const inverterMaxVoltage = Number(document.getElementById('inverterMaxVoltage').value);
                const panelVoc = Number(document.getElementById('panelVoc').value);
                const lowestTemperature = Number(document.getElementById('lowestTemperature').value);
                const targetPowerKw = Number(document.getElementById('targetSystemPower').value);
                const panelWatt = Number(document.getElementById('stringPanelWatt').value);

                if (!inverterMaxVoltage || !panelVoc || !targetPowerKw || !panelWatt) {
                    result.textContent = 'يرجى إدخال قيم صحيحة.';
                    return;
                }

                const coldFactor = 1 + Math.abs(Math.min(0, lowestTemperature)) * 0.0032;
                const effectiveVoc = panelVoc * coldFactor;
                const maxStringVoltage = inverterMaxVoltage * 0.98;
                const panelsPerString = Math.max(1, Math.floor(maxStringVoltage / effectiveVoc));

                const totalPanels = Math.ceil((targetPowerKw * 1000) / panelWatt);
                const stringsNeeded = Math.max(1, Math.ceil(totalPanels / panelsPerString));
                const finalPanels = stringsNeeded * panelsPerString;
                const sparePanels = finalPanels - totalPanels;

                result.innerHTML = `
                    <strong>الألواح لكل سلسلة:</strong> ${panelsPerString} لوحة<br>
                    <strong>عدد السلاسل المطلوب:</strong> ${stringsNeeded} سلسلة متوازية<br>
                    <strong>إجمالي الألواح المستثمر:</strong> ${finalPanels} لوحة (فائض ${sparePanels} للاحتياط)<br>
                    <strong>جهد السلسلة المتوقع:</strong> ${formatNumber(panelsPerString * panelVoc, { maximumFractionDigits: 0 })} V عند STC
                `;
            });
        }

        function renderAreaCalculator(container) {
            container.innerHTML = `
                <div class="tool-header">
                    <h2>حاسبة المساحة</h2>
                    <p>قدّر المساحة المتاحة على السطح وعدد الألواح التي يمكن تركيبها مع مراعاة مسافات الخدمة والتهوية.</p>
                </div>
                <form class="tool-form" id="areaCalculatorForm">
                    <label>طول السطح (متر)
                        <input type="number" min="1" step="0.1" value="28" id="roofLength" required>
                    </label>
                    <label>عرض السطح (متر)
                        <input type="number" min="1" step="0.1" value="12" id="roofWidth" required>
                    </label>
                    <label>نسبة المساحة المخصصة للممرات والخدمات (%)
                        <input type="number" min="0" max="40" step="1" value="12" id="serviceAllowance" required>
                    </label>
                    <label>قدرة اللوح الواحد (W)
                        <input type="number" min="200" max="700" step="10" value="550" id="areaPanelWatt" required>
                    </label>
                    <button type="submit" class="tool-button">احسب المساحة المتاحة</button>
                </form>
                <div class="tool-output" id="areaCalculatorResult">أدخل أبعاد السطح لحساب الاستيعاب الأقصى.</div>
            `;

            const form = container.querySelector('#areaCalculatorForm');
            const result = container.querySelector('#areaCalculatorResult');

            form.addEventListener('submit', function (event) {
                event.preventDefault();
                const length = Number(document.getElementById('roofLength').value);
                const width = Number(document.getElementById('roofWidth').value);
                const allowance = Number(document.getElementById('serviceAllowance').value) / 100;
                const panelWatt = Number(document.getElementById('areaPanelWatt').value);

                if (!length || !width || !panelWatt) {
                    result.textContent = 'تأكد من صحة الأبعاد المدخلة.';
                    return;
                }

                const roofArea = length * width;
                const usableArea = roofArea * Math.max(0, 1 - allowance);
                const panelArea = 1.95;
                const panelCount = Math.floor(usableArea / panelArea);
                const capacityKw = (panelCount * panelWatt) / 1000;

                result.innerHTML = `
                    <strong>المساحة الإجمالية:</strong> ${formatNumber(roofArea, { maximumFractionDigits: 1 })} م²<br>
                    <strong>المساحة المتاحة بعد الخصم:</strong> ${formatNumber(usableArea, { maximumFractionDigits: 1 })} م²<br>
                    <strong>عدد الألواح الممكن:</strong> ${panelCount} لوحة تقريبًا<br>
                    <strong>القدرة الإجمالية المحتملة:</strong> ${formatNumber(capacityKw, { maximumFractionDigits: 2 })} kW
                `;
            });
        }

        function renderBatteryCalculator(container) {
            container.innerHTML = `
                <div class="tool-header">
                    <h2>حاسبة البطاريات</h2>
                    <p>احسب سعة التخزين المطلوبة بناءً على الاستهلاك اليومي وعدد أيام الاستقلالية المطلوبة.</p>
                </div>
                <form class="tool-form" id="batteryCalculatorForm">
                    <label>الاستهلاك اليومي (kWh)
                        <input type="number" min="1" step="0.1" value="45" id="batteryDailyLoad" required>
                    </label>
                    <label>أيام الاستقلالية المطلوبة (دون شمس)
                        <input type="number" min="1" max="7" value="2" id="batteryAutonomy" required>
                    </label>
                    <label>جهد نظام البطاريات (V)
                        <input type="number" min="12" max="600" step="12" value="240" id="batteryVoltage" required>
                    </label>
                    <label>العمق المسموح لتفريغ البطارية (%)
                        <input type="number" min="20" max="90" value="70" id="batteryDod" required>
                    </label>
                    <label>سعة البطارية الواحدة (Ah)
                        <input type="number" min="50" max="500" value="200" id="batteryUnitCapacity" required>
                    </label>
                    <button type="submit" class="tool-button">احسب التخزين</button>
                </form>
                <div class="tool-output" id="batteryCalculatorResult">أدخل بياناتك للحساب.</div>
            `;

            const form = container.querySelector('#batteryCalculatorForm');
            const result = container.querySelector('#batteryCalculatorResult');

            form.addEventListener('submit', function (event) {
                event.preventDefault();
                const dailyLoad = Number(document.getElementById('batteryDailyLoad').value);
                const autonomyDays = Number(document.getElementById('batteryAutonomy').value);
                const systemVoltage = Number(document.getElementById('batteryVoltage').value);
                const dod = Number(document.getElementById('batteryDod').value) / 100;
                const unitCapacity = Number(document.getElementById('batteryUnitCapacity').value);

                if (!dailyLoad || !autonomyDays || !systemVoltage || !dod || !unitCapacity) {
                    result.textContent = 'يرجى إدخال قيم صالحة.';
                    return;
                }

                const requiredEnergyWh = dailyLoad * autonomyDays * 1000;
                const adjustedEnergyWh = requiredEnergyWh / Math.max(0.1, dod);
                const totalAh = adjustedEnergyWh / systemVoltage;
                const batteriesInSeries = Math.max(1, Math.round(systemVoltage / 48));
                const batteriesTotal = Math.ceil(totalAh / unitCapacity);
                const parallelStrings = Math.max(1, Math.ceil(batteriesTotal / batteriesInSeries));

                result.innerHTML = `
                    <strong>الطاقة المخزنة المطلوبة:</strong> ${formatNumber(adjustedEnergyWh / 1000, { maximumFractionDigits: 1 })} kWh<br>
                    <strong>سعة البنك الكلية:</strong> ${formatNumber(totalAh, { maximumFractionDigits: 0 })} Ah عند ${systemVoltage}V<br>
                    <strong>عدد البطاريات الكلي:</strong> ${batteriesTotal} بطارية (${parallelStrings} صفوف متوازية × ${batteriesInSeries} على التوالي)<br>
                    <strong>نسبة التفريغ المستخدمة:</strong> ${formatNumber(dod * 100, { maximumFractionDigits: 0 })}%
                `;
            });
        }

        function renderWireSizing(container) {
            container.innerHTML = `
                <div class="tool-header">
                    <h2>حساب حجم الأسلاك</h2>
                    <p>احصل على توصية لسماكة الكابل بناءً على التيار والمسافة ونسبة هبوط الجهد المسموح بها.</p>
                </div>
                <form class="tool-form" id="wireSizingForm">
                    <label>التيار المستمر (A)
                        <input type="number" min="1" step="0.1" value="72" id="wireCurrent" required>
                    </label>
                    <label>المسافة الإجمالية (متر)
                        <input type="number" min="1" step="0.1" value="40" id="wireDistance" required>
                    </label>
                    <label>جهد النظام (V)
                        <input type="number" min="12" max="1500" value="600" id="wireVoltage" required>
                    </label>
                    <label>نسبة هبوط الجهد المسموحة (%)
                        <input type="number" min="1" max="5" step="0.1" value="2.5" id="wireDrop" required>
                    </label>
                    <button type="submit" class="tool-button">احسب الحجم</button>
                </form>
                <div class="tool-output" id="wireSizingResult">أدخل القيم لمعرفة مقطع السلك المناسب.</div>
            `;

            const form = container.querySelector('#wireSizingForm');
            const result = container.querySelector('#wireSizingResult');

            form.addEventListener('submit', function (event) {
                event.preventDefault();
                const current = Number(document.getElementById('wireCurrent').value);
                const distance = Number(document.getElementById('wireDistance').value);
                const voltage = Number(document.getElementById('wireVoltage').value);
                const dropPercent = Number(document.getElementById('wireDrop').value);

                if (!current || !distance || !voltage || !dropPercent) {
                    result.textContent = 'تأكد من صحة الأرقام المدخلة.';
                    return;
                }

                const dropVoltage = voltage * (dropPercent / 100);
                const areaRequired = (2 * distance * current) / (56 * dropVoltage);
                const awgTable = [
                    { label: '10 AWG', area: 5.26 },
                    { label: '8 AWG', area: 8.37 },
                    { label: '6 AWG', area: 13.3 },
                    { label: '4 AWG', area: 21.2 },
                    { label: '2 AWG', area: 33.6 },
                    { label: '1/0 AWG', area: 53.5 },
                    { label: '2/0 AWG', area: 67.4 },
                    { label: '3/0 AWG', area: 85.0 },
                    { label: '4/0 AWG', area: 107.0 }
                ];

                const recommended = awgTable.find(item => item.area >= areaRequired) || awgTable[awgTable.length - 1];
                const actualDrop = (2 * distance * current) / (56 * recommended.area);
                const actualPercent = (actualDrop / voltage) * 100;

                result.innerHTML = `
                    <strong>المقطع المطلوب نظريًا:</strong> ${formatNumber(areaRequired, { maximumFractionDigits: 2 })} mm²<br>
                    <strong>المقاس الموصى به:</strong> ${recommended.label} (${recommended.area.toFixed(1)} mm²)<br>
                    <strong>هبوط الجهد الفعلي:</strong> ${formatNumber(actualDrop, { maximumFractionDigits: 2 })} V (${formatNumber(actualPercent, { maximumFractionDigits: 2 })}%)<br>
                    <span class="tool-note">تم الاعتماد على كابل نحاس بدرجة حرارة تشغيل 75°C.</span>
                `;
            });
        }

        function renderPricingData(container) {
            const dataset = [
                { type: 'Panels', name: 'Canadian Solar 550W', price: 118, unit: 'JD', availability: 'متوفر 32 وحدة', lead: 'تسليم خلال 4 أيام' },
                { type: 'Panels', name: 'JA Solar 565W Bifacial', price: 132, unit: 'JD', availability: 'متوفر 20 وحدة', lead: 'تسليم خلال 6 أيام' },
                { type: 'Inverters', name: 'Huawei SUN2000 60KTLM', price: 4890, unit: 'JD', availability: 'مخزون محدود', lead: 'تأكيد خلال 48 ساعة' },
                { type: 'Inverters', name: 'Sungrow SG125CX', price: 4250, unit: 'JD', availability: 'متوفر للطلب', lead: 'شحن خلال أسبوع' },
                { type: 'Batteries', name: 'Pylontech Force-L2 10.65kWh', price: 2980, unit: 'JD', availability: 'طلب مسبق', lead: '3 أسابيع' },
                { type: 'Balance', name: 'كابلات DC مزدوجة 4 مم²', price: 1.8, unit: 'JD/متر', availability: 'في المخزون', lead: 'تسليم فوري' },
                { type: 'Balance', name: 'قواطع DC 1000V', price: 64, unit: 'JD', availability: 'في المخزون', lead: 'تسليم فوري' }
            ];

            container.innerHTML = `
                <div class="tool-header">
                    <h2>بيانات التسعير</h2>
                    <p>تحدّث الأسعار تلقائيًا بالاعتماد على قاعدة بيانات المورّدين، ويمكن تصفية النتائج بحسب نوع المكوّن.</p>
                </div>
                <div class="tool-form">
                    <label>اختر فئة المكونات
                        <select id="pricingFilter">
                            <option value="all">جميع المكونات</option>
                            <option value="Panels">ألواح شمسية</option>
                            <option value="Inverters">عاكسات</option>
                            <option value="Batteries">بطاريات</option>
                            <option value="Balance">معدات إضافية</option>
                        </select>
                    </label>
                </div>
                <div class="tool-table-wrapper">
                    <table class="tool-table">
                        <thead>
                            <tr>
                                <th>المكوّن</th>
                                <th>السعر</th>
                                <th>التوفر</th>
                                <th>المدة المتوقعة</th>
                            </tr>
                        </thead>
                        <tbody id="pricingTableBody"></tbody>
                    </table>
                </div>
                <p class="tool-note">الأسعار بالدينار الأردني وتشمل خدمة الدعم الفني.</p>
            `;

            const filter = container.querySelector('#pricingFilter');
            const tbody = container.querySelector('#pricingTableBody');

            function renderRows() {
                const value = filter.value;
                const rows = dataset.filter(item => value === 'all' || item.type === value);
                tbody.innerHTML = rows.map(item => `
                    <tr>
                        <td>${item.name}</td>
                        <td>${formatNumber(item.price, { maximumFractionDigits: 2 })} ${item.unit}</td>
                        <td>${item.availability}</td>
                        <td>${item.lead}</td>
                    </tr>
                `).join('');
            }

            filter.addEventListener('change', renderRows);
            renderRows();
        }

        function renderReportBuilder(container) {
            container.innerHTML = `
                <div class="tool-header">
                    <h2>منشئ التقرير الذكي</h2>
                    <p>أدخل تفاصيل المشروع للحصول على تقرير مختصر يمكن نسخه مباشرة وإرساله للعميل.</p>
                </div>
                <form class="tool-form" id="reportBuilderForm">
                    <label>اسم المشروع
                        <input type="text" id="reportProject" placeholder="مشروع الطاقة لمجمع XYZ" required>
                    </label>
                    <label>اسم العميل
                        <input type="text" id="reportClient" placeholder="شركة المستقبل للطاقة" required>
                    </label>
                    <label>موقع المشروع
                        <input type="text" id="reportLocation" placeholder="عمان - الأردن" required>
                    </label>
                    <label>قدرة النظام المقترحة (kW)
                        <input type="number" id="reportSystemSize" value="120" step="0.1" required>
                    </label>
                    <label>نسبة التوفير المتوقعة (%)
                        <input type="number" id="reportSavings" value="27" step="1" required>
                    </label>
                    <label>ملاحظات إضافية
                        <textarea id="reportNotes" rows="3" placeholder="يشمل الحل بطاريات ليثيوم لضمان استقلالية لمدة يومين."></textarea>
                    </label>
                    <button type="submit" class="tool-button">إنشاء التقرير</button>
                </form>
                <div class="tool-report-output" id="reportBuilderOutput">سيظهر التقرير هنا بعد إدخال البيانات.</div>
            `;

            const form = container.querySelector('#reportBuilderForm');
            const output = container.querySelector('#reportBuilderOutput');

            form.addEventListener('submit', function (event) {
                event.preventDefault();
                const project = (document.getElementById('reportProject').value || '').trim();
                const client = (document.getElementById('reportClient').value || '').trim();
                const location = (document.getElementById('reportLocation').value || '').trim();
                const systemSize = Number(document.getElementById('reportSystemSize').value);
                const savings = Number(document.getElementById('reportSavings').value);
                const notes = (document.getElementById('reportNotes').value || '').trim();

                if (!project || !client || !location || !systemSize || !savings) {
                    output.textContent = 'يرجى إكمال الحقول المطلوبة.';
                    return;
                }

                const report = `التقرير التنفيذي لمشروع ${project}
العميل: ${client}
الموقع: ${location}

• القدرة المقترحة للنظام: ${formatNumber(systemSize, { maximumFractionDigits: 1 })} kW
• نسبة التوفير السنوي المقدرة: ${formatNumber(savings, { maximumFractionDigits: 0 })}%
• خفض انبعاثات ثاني أكسيد الكربون: ${formatNumber(systemSize * 1.4, { maximumFractionDigits: 1 })} طن سنويًا

التوصيات:
- تركيب منظومة مراقبة لحظية مع تنبيهات للصيانة.
- جدولة صيانة وقائية كل 6 أشهر.
${notes ? `- ملاحظات إضافية: ${notes}` : ''}`;

                output.textContent = report;
            });
        }

        const energyToolRegistry = {
            'dashboard': { type: 'static', render: renderEnergyDashboard },
            'live-simulation': {
                type: 'static',
                render: renderLiveSimulation,
                onDeactivate: () => {
                    const controller = window.liveSimulationController;
                    if (controller && typeof controller.onDeactivate === 'function') {
                        try { controller.onDeactivate(); } catch (e) {}
                    }
                }
            },
            'system-sizing': { type: 'static', render: renderSystemSizing },
            'field-inspector': { type: 'static', render: renderFieldInspector },
            'string-config': { type: 'static', render: renderStringConfiguration },
            'area-calculator': { type: 'static', render: renderAreaCalculator },
            'battery-calculator': { type: 'static', render: renderBatteryCalculator },
            'wire-sizing': { type: 'static', render: renderWireSizing },
            'pricing-data': { type: 'static', render: renderPricingData },
            'report-builder': { type: 'static', render: renderReportBuilder }
        };

        function resetEnergyModalLayout(showWelcome) {
            const chatContentArea = document.getElementById('chatContentArea');
            const welcomeContent = document.getElementById('welcomeContent');
            const centerInput = document.getElementById('centerInputContainer');
            const bottomInput = document.getElementById('bottomInputContainer');
            const dynamicContainer = document.getElementById('toolDynamicContainer');

            if (chatContentArea) {
                chatContentArea.style.display = 'flex';
                chatContentArea.style.flexDirection = 'column';
                chatContentArea.style.height = 'calc(100vh - 120px)';
                chatContentArea.style.overflowY = showWelcome ? 'visible' : 'auto';
                chatContentArea.style.alignItems = showWelcome ? 'center' : 'stretch';
                chatContentArea.style.justifyContent = showWelcome ? 'center' : 'flex-start';
                chatContentArea.style.textAlign = showWelcome ? 'center' : 'left';
                chatContentArea.style.padding = showWelcome ? '40px 20px' : '0';
            }

            if (dynamicContainer) {
                dynamicContainer.innerHTML = '';
                dynamicContainer.classList.toggle('active', !showWelcome);
                dynamicContainer.style.display = showWelcome ? 'none' : 'flex';
            }

            if (welcomeContent) {
                welcomeContent.style.display = showWelcome ? 'flex' : 'none';
                if (showWelcome) {
                    welcomeContent.style.flexDirection = 'column';
                    welcomeContent.style.alignItems = 'center';
                    welcomeContent.style.justifyContent = 'center';
                    welcomeContent.style.gap = '24px';
                    welcomeContent.style.width = '100%';
                    welcomeContent.style.visibility = 'visible';
                    welcomeContent.style.opacity = '1';
                }
            }

            if (centerInput) {
                centerInput.style.display = showWelcome ? 'flex' : 'none';
            }

            if (bottomInput) {
                bottomInput.style.display = 'none';
            }
        }

        function activateEnergyTool(toolId) {
            if (!toolId) return;
            const modal = document.getElementById('energyOptimizationModal');
            if (!modal) return;

            const previousTool = currentEnergyTool;
            if (previousTool && previousTool !== toolId) {
                const previousConfig = energyToolRegistry[previousTool];
                if (previousConfig && typeof previousConfig.onDeactivate === 'function') {
                    try { previousConfig.onDeactivate(); } catch (e) {}
                }
            }

            currentEnergyTool = toolId;

            modal.querySelectorAll('.chat-item[data-tool]').forEach(item => {
                const isActive = item.getAttribute('data-tool') === toolId;
                item.classList.toggle('active', isActive);
            });

            const config = energyToolRegistry[toolId];
            if (!config) return;

            if (config.type === 'chat') {
                resetEnergyModalLayout(true);
                if (typeof config.onActivate === 'function') {
                    config.onActivate();
                }
                const input = document.getElementById('modalMessageInput');
                if (input) {
                    setTimeout(() => input.focus(), 150);
                }
                return;
            }

            resetEnergyModalLayout(false);
            const container = document.getElementById('toolDynamicContainer');
            if (container && typeof config.render === 'function') {
                config.render(container);
                container.scrollTop = 0;
                if (typeof config.onActivate === 'function') {
                    try { config.onActivate(); } catch (e) {}
                }
            }

        }

        function initializeEnergyTools() {
            const modal = document.getElementById('energyOptimizationModal');
            if (!modal) return;

            const items = modal.querySelectorAll('.chat-item[data-tool]');
            items.forEach(item => {
                item.addEventListener('click', () => {
                    const toolId = item.getAttribute('data-tool');
                    activateEnergyTool(toolId);
                });
            });

            const activeItem = modal.querySelector('.chat-item.active[data-tool]') || items[0];
            if (activeItem) {
                const defaultTool = activeItem.getAttribute('data-tool');
                currentEnergyTool = defaultTool || 'dashboard';
                activateEnergyTool(currentEnergyTool);
            }
        }

















        function addModalMessage(content, sender) {
            const welcomeContent = document.getElementById('welcomeContent');
            const scroller = document.querySelector('.modal-chat-area');
            const nearBottom = (() => {
                if (!scroller) return true;
                const delta = scroller.scrollHeight - scroller.scrollTop - scroller.clientHeight;
                return delta < 200; // auto-scroll only if user is near bottom
            })();

            // Replace welcome content with messages container on first message
            if (!welcomeContent.querySelector('.messages-list')) {
                // Clear welcome content
                welcomeContent.innerHTML = '';

            // Create messages container
            const messagesContainer = document.createElement('div');
            messagesContainer.className = 'messages-list';
            messagesContainer.style.display = 'flex';
            messagesContainer.style.flexDirection = 'column';
            messagesContainer.style.gap = '0px'; // no space between message and reply
            messagesContainer.style.width = '100%';
            messagesContainer.style.maxWidth = '800px';

                welcomeContent.appendChild(messagesContainer);
            }

            const messagesContainer = welcomeContent.querySelector('.messages-list');

            // Create message
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;



            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            // Basic formatting: escape HTML and preserve line breaks
            const safe = (content || '')
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/\n/g, '<br>');
            messageContent.innerHTML = safe;
            // Keep inline styles consistent with original centered design
            messageContent.style.width = '100%';
            messageContent.style.maxWidth = '700px';
            messageContent.style.padding = '20px 24px';
            messageContent.style.backgroundColor = 'rgba(255, 255, 255, 0.08)';
            messageContent.style.border = '1px solid rgba(255, 255, 255, 0.12)';
            messageContent.style.borderRadius = '20px';
            messageContent.style.color = 'white';
            messageContent.style.lineHeight = '1.7';
            messageContent.style.textAlign = 'center';
            messageContent.style.fontSize = '15px';
            messageContent.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.2)';
            messageContent.style.backdropFilter = 'blur(10px)';
            messageContent.style.margin = '0 auto';

            messageDiv.appendChild(messageContent);

            // Add message to messages container
            messagesContainer.appendChild(messageDiv);

            if (nearBottom && scroller) {
                scrollToBottomSafe();
            }
            updateBottomPadding();
            ensureInputSpacer();
        }

        // Call backend AI for a smarter response
        async function requestAIResponse(userMessage) {
            // Maintain conversation context
            energyModalConversation.push({ role: 'user', content: userMessage });

            // Build endpoint from configSystem if available
            let chatURL = '/api/chat';
            try {
                if (window.configSystem) {
                    chatURL = window.configSystem.getAPIEndpoint('chat');
                }
            } catch (e) {}

            const resp = await fetch(chatURL, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ messages: energyModalConversation, temperature: 0.3, max_tokens: 600 })
            });

            if (!resp.ok) {
                throw new Error('Bad response from AI backend');
            }
            const data = await resp.json();
            const ai = data?.choices?.[0]?.message?.content || 'I could not generate a response.';

            // Save assistant reply to conversation
            energyModalConversation.push({ role: 'assistant', content: ai });

            return ai;
        }

        function generateModalResponse(userMessage) {
            const responses = {
                'energy bill': 'I can help you analyze your energy consumption! Please share your recent electricity bill, and I\'ll provide:\n\n• Peak usage patterns\n• Cost breakdown analysis\n• Optimization opportunities\n• Potential savings calculation\n\nYou can upload an image or PDF of your bill.',

                'solar': 'Great choice! Solar panels can significantly reduce your energy costs. Here\'s what I need to provide personalized recommendations:\n\n• Your location\n• Average monthly electricity bill\n• Roof size and orientation\n• Current energy usage patterns\n\nBased on this, I can calculate potential savings and ROI.',

                'smart home': 'Smart home devices can reduce energy consumption by 10-25%! Here are my top recommendations:\n\n🌡️ Smart Thermostats (15-20% savings)\n💡 Smart LED Bulbs (75% less energy)\n🔌 Smart Plugs (eliminate phantom loads)\n📱 Energy Monitoring Systems\n\nWhich area would you like to start with?',

                'costs': 'Here are proven ways to reduce your electricity costs:\n\n1. **Immediate Actions:**\n   • Switch to LED lighting\n   • Unplug unused devices\n   • Adjust thermostat settings\n\n2. **Medium-term:**\n   • Upgrade to efficient appliances\n   • Install smart thermostats\n   • Improve insulation\n\n3. **Long-term:**\n   • Solar panel installation\n   • Energy storage systems\n\nWould you like detailed guidance on any of these?'
            };

            for (const [key, response] of Object.entries(responses)) {
                if (userMessage.toLowerCase().includes(key)) {
                    return response;
                }
            }

            return 'I\'m here to help with all your energy optimization needs! I can assist with:\n\n• Energy bill analysis\n• Solar panel recommendations\n• Smart home solutions\n• Cost reduction strategies\n• Efficiency improvements\n\nWhat specific area would you like to explore?';
        }

        // Auto-resize textarea for modal
        document.addEventListener('DOMContentLoaded', function() {
            const modalMessageInput = document.getElementById('modalMessageInput');
            if (modalMessageInput) {
                modalMessageInput.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = Math.min(this.scrollHeight, 120) + 'px';
                    try { updateBottomPadding(); } catch (e) {}
                });

                modalMessageInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        sendModalMessage();
                    }
                });

                // Bottom input event listeners
                const modalMessageInputBottom = document.getElementById('modalMessageInputBottom');
                if (modalMessageInputBottom) {
                    modalMessageInputBottom.addEventListener('input', function() {
                        this.style.height = 'auto';
                        this.style.height = Math.min(this.scrollHeight, 120) + 'px';
                        try { updateBottomPadding(); } catch (e) {}
                    });

                    modalMessageInputBottom.addEventListener('keydown', function(e) {
                        if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            sendModalMessageFromBottom();
                        }
                    });
                }
            }
            try { updateBottomPadding(); } catch (e) {}
            window.addEventListener('resize', function(){ try { updateBottomPadding(); } catch (e) {} });
        });

        // Removed PM modal auto-resize (standalone page)
    </script>



    <!-- Auth system will be loaded by auth-system.js -->

    <!-- Glassmorphism System - Priority Load -->
    <script src="js/glassmorphism.js"></script>

    <!-- Core Systems - Load first -->
    <script src="js/config.js"></script>
    <script src="js/state-management.js"></script>
    <script src="js/security-system.js"></script>
    <script src="js/performance-optimizations.js"></script>
    <script src="js/notification-system.js"></script>
    <script src="js/analytics-system.js"></script>

    <!-- Core JavaScript -->
    <script src="js/jarvis-config.js"></script>
    <!-- Language system removed - English only -->
    <script src="js/auth-system.js"></script>
    <script src="js/embedded-map.js"></script>

    <script src="js/main.js"></script>

    <!-- Energy Security Modal Scripts (independent from Optimization) -->
    <script>
        function openEnergySecurity() {
            const modal = document.getElementById('energySecurityModal');
            if (!modal) return;
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
            // Normalize welcome title visibility/size immediately
            const wc = document.getElementById('welcomeContentSec');
            const wt = wc ? wc.querySelector('.modal-welcome-title') : null;
            if (wc) { wc.style.display='flex'; wc.style.visibility='visible'; wc.style.opacity='1'; }
            if (wt) { wt.style.display='block'; wt.style.visibility='visible'; wt.style.opacity='1'; wt.style.fontSize='36px'; }
        }

        function closeEnergySecurity() {
            const modal = document.getElementById('energySecurityModal');
            if (!modal) return;
            modal.classList.remove('active');
            document.body.style.overflow = 'auto';
        }

        let energySecurityConversation = [];

        function startNewChatSecurity() {
            const welcomeContent = document.getElementById('welcomeContentSec');
            const centerInput = document.getElementById('centerInputContainerSec');
            const bottomInput = document.getElementById('bottomInputContainerSec');
            const chatContentArea = document.getElementById('chatContentAreaSec');

            // Clear existing messages
            const existingMessages = chatContentArea.querySelectorAll('.message');
            existingMessages.forEach(el => el.remove());
            const list = chatContentArea.querySelector('.messages-list');
            if (list) list.remove();

            // Reset styles
            if (chatContentArea) {
                chatContentArea.style.display = 'flex';
                chatContentArea.style.flexDirection = 'column';
                chatContentArea.style.justifyContent = 'center';
                chatContentArea.style.alignItems = 'center';
                chatContentArea.style.textAlign = 'center';
                chatContentArea.style.padding = '40px 20px';
                chatContentArea.style.position = 'relative';
                chatContentArea.style.height = 'calc(100vh - 120px)';
                chatContentArea.style.overflow = 'visible';
                chatContentArea.classList.remove('chat-mode');
            }

            energySecurityConversation = [];

            // Show center input
            if (centerInput) centerInput.style.display = 'flex';
            if (bottomInput) bottomInput.style.display = 'none';

            const top = document.getElementById('modalMessageInputSec');
            const bottom = document.getElementById('modalMessageInputBottomSec');
            if (top) top.value = '';
            if (bottom) bottom.value = '';
        }

        function switchToChatModeSec() {
            const welcomeContent = document.getElementById('welcomeContentSec');
            const centerInput = document.getElementById('centerInputContainerSec');
            const bottomInput = document.getElementById('bottomInputContainerSec');
            const chatContentArea = document.getElementById('chatContentAreaSec');
            if (centerInput) centerInput.style.display = 'none';
            if (welcomeContent) {
                welcomeContent.style.textAlign = 'left';
                welcomeContent.style.maxWidth = '100%';
                welcomeContent.style.display = 'flex';
                welcomeContent.style.flexDirection = 'column';
                welcomeContent.style.alignItems = 'center';
                welcomeContent.style.justifyContent = 'flex-start';
                welcomeContent.style.height = 'auto';
                welcomeContent.style.paddingBottom = '0px';
            }
            if (chatContentArea) {
                chatContentArea.style.justifyContent = 'flex-start';
                chatContentArea.style.textAlign = 'left';
                chatContentArea.style.alignItems = 'stretch';
                chatContentArea.style.padding = '20px';
            }
            if (bottomInput) bottomInput.style.display = 'flex';
            updateBottomPaddingSec();
        }

        function scrollToBottomSafeSec() {
            const scroller = document.querySelector('#energySecurityModal .modal-chat-area');
            if (!scroller) return;
            const input = document.getElementById('bottomInputContainerSec');
            const inputH = input ? input.getBoundingClientRect().height : 160;
            const extra = 24;
            const target = Math.max(0, scroller.scrollHeight - scroller.clientHeight - inputH - extra);
            scroller.scrollTo({ top: target, behavior: 'smooth' });
        }

        function addModalMessageSec(content, sender) {
            const welcomeContent = document.getElementById('welcomeContentSec');
            const scroller = document.querySelector('#energySecurityModal .modal-chat-area');
            const nearBottom = (() => {
                if (!scroller) return true;
                const delta = scroller.scrollHeight - scroller.scrollTop - scroller.clientHeight;
                return delta < 200;
            })();
            if (!welcomeContent.querySelector('.messages-list')) {
                welcomeContent.innerHTML = '';
                const messagesContainer = document.createElement('div');
                messagesContainer.className = 'messages-list';
                messagesContainer.style.display = 'flex';
                messagesContainer.style.flexDirection = 'column';
                messagesContainer.style.gap = '0px';
                messagesContainer.style.width = '100%';
                messagesContainer.style.maxWidth = '800px';
                welcomeContent.appendChild(messagesContainer);
            }
            const messagesContainer = welcomeContent.querySelector('.messages-list');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            const safe = (content || '')
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/\n/g, '<br>');
            messageContent.innerHTML = safe;
            messageContent.style.width = '100%';
            messageContent.style.maxWidth = '700px';
            messageContent.style.padding = '20px 24px';
            messageContent.style.backgroundColor = 'rgba(255, 255, 255, 0.08)';
            messageContent.style.border = '1px solid rgba(255, 255, 255, 0.12)';
            messageContent.style.borderRadius = '20px';
            messageContent.style.color = 'white';
            messageContent.style.lineHeight = '1.7';
            messageContent.style.textAlign = 'center';
            messageContent.style.fontSize = '15px';
            messageContent.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.2)';
            messageContent.style.backdropFilter = 'blur(10px)';
            messageContent.style.margin = '0 auto';
            messageDiv.appendChild(messageContent);
            messagesContainer.appendChild(messageDiv);
            if (nearBottom && scroller) scrollToBottomSafeSec();
            updateBottomPaddingSec();
            ensureInputSpacerSec();
        }

        async function requestAIResponseSec(userMessage) {
            energySecurityConversation.push({ role: 'user', content: userMessage });
            let chatURL = '/api/chat';
            try { if (window.configSystem) chatURL = window.configSystem.getAPIEndpoint('chat'); } catch (e) {}
            const resp = await fetch(chatURL, {
                method: 'POST', headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ messages: energySecurityConversation, temperature: 0.3, max_tokens: 600 })
            });
            if (!resp.ok) throw new Error('Bad response from AI backend');
            const data = await resp.json();
            const ai = data?.choices?.[0]?.message?.content || 'I could not generate a response.';
            energySecurityConversation.push({ role: 'assistant', content: ai });
            return ai;
        }

        async function sendModalMessageSec() {
            const messageInput = document.getElementById('modalMessageInputSec');
            if (!messageInput) return;
            const message = messageInput.value.trim();
            if (!message) return;
            switchToChatModeSec();
            addModalMessageSec(message, 'user');
            try { scrollToBottomSafeSec(); updateBottomPaddingSec(); } catch (e) {}
            messageInput.value = '';
            try { const ai = await requestAIResponseSec(message); addModalMessageSec(ai, 'ai'); updateBottomPaddingSec(); }
            catch (e) { addModalMessageSec('Sorry, I could not reach the AI service. Please try again.', 'ai'); }
        }

        async function sendModalMessageFromBottomSec() {
            const messageInput = document.getElementById('modalMessageInputBottomSec');
            if (!messageInput) return;
            const message = messageInput.value.trim();
            if (!message) return;
            addModalMessageSec(message, 'user');
            try { scrollToBottomSafeSec(); updateBottomPaddingSec(); } catch (e) {}
            messageInput.value = '';
            messageInput.style.height = 'auto';
            try { const ai = await requestAIResponseSec(message); addModalMessageSec(ai, 'ai'); updateBottomPaddingSec(); }
            catch (e) { addModalMessageSec('Sorry, I could not reach the AI service. Please try again.', 'ai'); }
        }

        function updateBottomPaddingSec() {
            const input = document.getElementById('bottomInputContainerSec');
            const scroller = document.querySelector('#energySecurityModal .modal-chat-area');
            const lists = document.querySelectorAll('#energySecurityModal .messages-list, #energySecurityModal .chat-content-area');
            const h = input ? input.getBoundingClientRect().height : 0;
            const pad = Math.min(window.innerHeight * 0.6, Math.max(160, Math.round(h + 56)));
            if (scroller) scroller.style.paddingBottom = pad + 'px';
            lists.forEach(el => el.style.paddingBottom = pad + 'px');
            ensureInputSpacerSec();
        }

        function ensureInputSpacerSec() {
            try {
                const list = document.querySelector('#energySecurityModal .messages-list');
                const input = document.getElementById('bottomInputContainerSec');
                if (!list || !input) return;
                let spacer = list.querySelector('#inputSpacerSec');
                const h = input.getBoundingClientRect().height || 0;
                const need = Math.round(h + 48);
                if (!spacer) {
                    spacer = document.createElement('div');
                    spacer.id = 'inputSpacerSec';
                    spacer.style.width = '100%';
                    spacer.style.height = need + 'px';
                    spacer.style.pointerEvents = 'none';
                    list.appendChild(spacer);
                } else {
                    spacer.style.height = need + 'px';
                    list.appendChild(spacer);
                }
            } catch (e) {}
        }

        document.addEventListener('DOMContentLoaded', function() {
            const inputTop = document.getElementById('modalMessageInputSec');
            if (inputTop) {
                inputTop.addEventListener('input', function(){ this.style.height='auto'; this.style.height=Math.min(this.scrollHeight,120)+'px'; try{updateBottomPaddingSec();}catch(e){} });
                inputTop.addEventListener('keydown', function(e){ if(e.key==='Enter' && !e.shiftKey){ e.preventDefault(); sendModalMessageSec(); } });
            }
            const inputBottom = document.getElementById('modalMessageInputBottomSec');
            if (inputBottom) {
                inputBottom.addEventListener('input', function(){ this.style.height='auto'; this.style.height=Math.min(this.scrollHeight,120)+'px'; try{updateBottomPaddingSec();}catch(e){} });
                inputBottom.addEventListener('keydown', function(e){ if(e.key==='Enter' && !e.shiftKey){ e.preventDefault(); sendModalMessageFromBottomSec(); } });
            }
            try { updateBottomPaddingSec(); } catch (e) {}
            window.addEventListener('resize', function(){ try { updateBottomPaddingSec(); } catch(e){} });
        });
    </script>

    <!-- Cloud Energy Modal Scripts (independent) -->
    <script>
        function openCloudEnergy() {
            const modal = document.getElementById('cloudEnergyModal');
            if (!modal) return;
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
        }

        function closeCloudEnergy() {
            const modal = document.getElementById('cloudEnergyModal');
            if (!modal) return;
            modal.classList.remove('active');
            document.body.style.overflow = 'auto';
        }

        let cloudConversation = [];

        function switchToChatModeCloud() {
            const welcomeContent = document.getElementById('welcomeContentCloud');
            const centerInput = document.getElementById('centerInputContainerCloud');
            const bottomInput = document.getElementById('bottomInputContainerCloud');
            const chatContentArea = document.getElementById('chatContentAreaCloud');
            if (centerInput) centerInput.style.display = 'none';
            if (welcomeContent) {
                welcomeContent.style.textAlign = 'left';
                welcomeContent.style.maxWidth = '100%';
                welcomeContent.style.display = 'flex';
                welcomeContent.style.flexDirection = 'column';
                welcomeContent.style.alignItems = 'center';
                welcomeContent.style.justifyContent = 'flex-start';
                welcomeContent.style.height = 'auto';
                welcomeContent.style.paddingBottom = '0px';
            }
            if (chatContentArea) {
                chatContentArea.style.justifyContent = 'flex-start';
                chatContentArea.style.textAlign = 'left';
                chatContentArea.style.alignItems = 'stretch';
                chatContentArea.style.padding = '20px';
            }
            if (bottomInput) bottomInput.style.display = 'flex';
            updateBottomPaddingCloud();
        }

        function scrollToBottomSafeCloud() {
            const scroller = document.querySelector('#cloudEnergyModal .modal-chat-area');
            if (!scroller) return;
            const input = document.getElementById('bottomInputContainerCloud');
            const inputH = input ? input.getBoundingClientRect().height : 160;
            const extra = 24;
            const target = Math.max(0, scroller.scrollHeight - scroller.clientHeight - inputH - extra);
            scroller.scrollTo({ top: target, behavior: 'smooth' });
        }

        function addModalMessageCloud(content, sender) {
            const welcomeContent = document.getElementById('welcomeContentCloud');
            const scroller = document.querySelector('#cloudEnergyModal .modal-chat-area');
            const nearBottom = (() => {
                if (!scroller) return true;
                const delta = scroller.scrollHeight - scroller.scrollTop - scroller.clientHeight;
                return delta < 200;
            })();
            if (!welcomeContent.querySelector('.messages-list')) {
                welcomeContent.innerHTML = '';
                const messagesContainer = document.createElement('div');
                messagesContainer.className = 'messages-list';
                messagesContainer.style.display = 'flex';
                messagesContainer.style.flexDirection = 'column';
                messagesContainer.style.gap = '0px';
                messagesContainer.style.width = '100%';
                messagesContainer.style.maxWidth = '800px';
                welcomeContent.appendChild(messagesContainer);
            }
            const messagesContainer = welcomeContent.querySelector('.messages-list');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}`;
            const messageContent = document.createElement('div');
            messageContent.className = 'message-content';
            const safe = (content || '')
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/\n/g, '<br>');
            messageContent.innerHTML = safe;
            messageContent.style.width = '100%';
            messageContent.style.maxWidth = '700px';
            messageContent.style.padding = '20px 24px';
            messageContent.style.backgroundColor = 'rgba(255, 255, 255, 0.08)';
            messageContent.style.border = '1px solid rgba(255, 255, 255, 0.12)';
            messageContent.style.borderRadius = '20px';
            messageContent.style.color = 'white';
            messageContent.style.lineHeight = '1.7';
            messageContent.style.textAlign = 'center';
            messageContent.style.fontSize = '15px';
            messageContent.style.boxShadow = '0 4px 20px rgba(0, 0, 0, 0.2)';
            messageContent.style.backdropFilter = 'blur(10px)';
            messageContent.style.margin = '0 auto';
            messageDiv.appendChild(messageContent);
            messagesContainer.appendChild(messageDiv);
            if (nearBottom && scroller) scrollToBottomSafeCloud();
            updateBottomPaddingCloud();
            ensureInputSpacerCloud();
        }

        async function requestAIResponseCloud(userMessage) {
            cloudConversation.push({ role: 'user', content: userMessage });
            let chatURL = '/api/chat';
            try { if (window.configSystem) chatURL = window.configSystem.getAPIEndpoint('chat'); } catch (e) {}
            const resp = await fetch(chatURL, { method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ messages: cloudConversation, temperature: 0.3, max_tokens: 600 }) });
            if (!resp.ok) throw new Error('Bad response from AI backend');
            const data = await resp.json();
            const ai = data?.choices?.[0]?.message?.content || 'I could not generate a response.';
            cloudConversation.push({ role: 'assistant', content: ai });
            return ai;
        }

        async function sendModalMessageCloud() {
            const messageInput = document.getElementById('modalMessageInputCloud');
            if (!messageInput) return;
            const message = messageInput.value.trim();
            if (!message) return;
            switchToChatModeCloud();
            addModalMessageCloud(message, 'user');
            try { scrollToBottomSafeCloud(); updateBottomPaddingCloud(); } catch (e) {}
            messageInput.value = '';
            try { const ai = await requestAIResponseCloud(message); addModalMessageCloud(ai, 'ai'); updateBottomPaddingCloud(); }
            catch (e) { addModalMessageCloud('Sorry, I could not reach the AI service. Please try again.', 'ai'); }
        }

        async function sendModalMessageFromBottomCloud() {
            const messageInput = document.getElementById('modalMessageInputBottomCloud');
            if (!messageInput) return;
            const message = messageInput.value.trim();
            if (!message) return;
            addModalMessageCloud(message, 'user');
            try { scrollToBottomSafeCloud(); updateBottomPaddingCloud(); } catch (e) {}
            messageInput.value = '';
            messageInput.style.height = 'auto';
            try { const ai = await requestAIResponseCloud(message); addModalMessageCloud(ai, 'ai'); updateBottomPaddingCloud(); }
            catch (e) { addModalMessageCloud('Sorry, I could not reach the AI service. Please try again.', 'ai'); }
        }

        function updateBottomPaddingCloud() {
            const input = document.getElementById('bottomInputContainerCloud');
            const scroller = document.querySelector('#cloudEnergyModal .modal-chat-area');
            const lists = document.querySelectorAll('#cloudEnergyModal .messages-list, #cloudEnergyModal .chat-content-area');
            const h = input ? input.getBoundingClientRect().height : 0;
            const pad = Math.min(window.innerHeight * 0.6, Math.max(160, Math.round(h + 56)));
            if (scroller) scroller.style.paddingBottom = pad + 'px';
            lists.forEach(el => el.style.paddingBottom = pad + 'px');
            ensureInputSpacerCloud();
        }

        function ensureInputSpacerCloud() {
            try {
                const list = document.querySelector('#cloudEnergyModal .messages-list');
                const input = document.getElementById('bottomInputContainerCloud');
                if (!list || !input) return;
                let spacer = list.querySelector('#inputSpacerCloud');
                const h = input.getBoundingClientRect().height || 0;
                const need = Math.round(h + 48);
                if (!spacer) {
                    spacer = document.createElement('div');
                    spacer.id = 'inputSpacerCloud';
                    spacer.style.width = '100%';
                    spacer.style.height = need + 'px';
                    spacer.style.pointerEvents = 'none';
                    list.appendChild(spacer);
                } else {
                    spacer.style.height = need + 'px';
                    list.appendChild(spacer);
                }
            } catch (e) {}
        }

        function startNewChatCloud() {
            const welcomeContent = document.getElementById('welcomeContentCloud');
            const centerInput = document.getElementById('centerInputContainerCloud');
            const bottomInput = document.getElementById('bottomInputContainerCloud');
            const chatContentArea = document.getElementById('chatContentAreaCloud');
            const existing = chatContentArea.querySelectorAll('.message');
            existing.forEach(n => n.remove());
            const list = chatContentArea.querySelector('.messages-list');
            if (list) list.remove();
            if (chatContentArea) {
                chatContentArea.style.display = 'flex';
                chatContentArea.style.flexDirection = 'column';
                chatContentArea.style.justifyContent = 'center';
                chatContentArea.style.alignItems = 'center';
                chatContentArea.style.textAlign = 'center';
                chatContentArea.style.padding = '40px 20px';
                chatContentArea.style.position = 'relative';
                chatContentArea.style.height = 'calc(100vh - 120px)';
                chatContentArea.style.overflow = 'visible';
                chatContentArea.classList.remove('chat-mode');
            }
            cloudConversation = [];
            if (centerInput) centerInput.style.display = 'flex';
            if (bottomInput) bottomInput.style.display = 'none';
            const top = document.getElementById('modalMessageInputCloud');
            const bottom = document.getElementById('modalMessageInputBottomCloud');
            if (top) top.value = '';
            if (bottom) bottom.value = '';
        }

        document.addEventListener('DOMContentLoaded', function() {
            const top = document.getElementById('modalMessageInputCloud');
            if (top) {
                top.addEventListener('input', function(){ this.style.height='auto'; this.style.height=Math.min(this.scrollHeight,120)+'px'; try{updateBottomPaddingCloud();}catch(e){} });
                top.addEventListener('keydown', function(e){ if(e.key==='Enter' && !e.shiftKey){ e.preventDefault(); sendModalMessageCloud(); } });
            }
            const bottom = document.getElementById('modalMessageInputBottomCloud');
            if (bottom) {
                bottom.addEventListener('input', function(){ this.style.height='auto'; this.style.height=Math.min(this.scrollHeight,120)+'px'; try{updateBottomPaddingCloud();}catch(e){} });
                bottom.addEventListener('keydown', function(e){ if(e.key==='Enter' && !e.shiftKey){ e.preventDefault(); sendModalMessageFromBottomCloud(); } });
            }
            try { updateBottomPaddingCloud(); } catch (e) {}
            window.addEventListener('resize', function(){ try { updateBottomPaddingCloud(); } catch(e){} });
        });
    </script>

    <!-- Three.js and Neon Cursor -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r134/three.min.js"></script>
    <script type="importmap">
    {
        "imports": {
            "three": "https://unpkg.com/three@0.139.2/build/three.module.js",
            "threejs-toys": "https://unpkg.com/threejs-toys@0.0.8/build/threejs-toys.module.cdn.min.js"
        }
    }
    </script>
    <script type="module" src="js/neon-cursor.js"></script>

    <!-- Sidebar hover functionality -->
    <script>
        // Sidebar hover functionality for all modals independently
        function initializeSidebar() {
            const containers = document.querySelectorAll('.energy-optimization-modal, .energy-security-modal, .cloud-energy-modal');
            containers.forEach(container => {
                const sidebar = container.querySelector('.modal-sidebar');
                const hoverArea = container.querySelector('.sidebar-hover-area');
                let isHovered = false;
                let hoverTimeout;
                if (hoverArea && sidebar) {
                    hoverArea.addEventListener('mouseenter', () => {
                        clearTimeout(hoverTimeout);
                        isHovered = true;
                        sidebar.style.transform = 'translateX(0)';
                        sidebar.style.opacity = '1';
                        sidebar.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.3)';
                    });
                    sidebar.addEventListener('mouseenter', () => {
                        clearTimeout(hoverTimeout);
                        isHovered = true;
                        sidebar.style.transform = 'translateX(0)';
                        sidebar.style.opacity = '1';
                        sidebar.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.3)';
                    });
                    sidebar.addEventListener('mouseleave', () => {
                        isHovered = false;
                        hoverTimeout = setTimeout(() => {
                            if (!isHovered) {
                                const isMobile = window.innerWidth <= 768;
                                const translateX = isMobile ? '-290px' : '-270px';
                                sidebar.style.transform = `translateX(${translateX})`;
                                sidebar.style.opacity = '0';
                                sidebar.style.boxShadow = 'none';
                            }
                        }, 500);
                    });
                    window.addEventListener('resize', () => {
                        if (!isHovered) {
                            const isMobile = window.innerWidth <= 768;
                            const translateX = isMobile ? '-290px' : '-270px';
                            sidebar.style.transform = `translateX(${translateX})`;
                            sidebar.style.opacity = '0';
                        }
                    });
                }
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            initializeSidebar();
        });
    </script>

    <!-- خلفية متحركة مع أشكال هندسية وتأثيرات متقدمة -->
    <div class="animated-background">
        <!-- الطبقة الأساسية للخلفية -->
        <div class="background-layer base-layer"></div>

        <!-- تأثير نبضة واضح -->
        <div class="pulse-effect"></div>

        <!-- الأشكال الهندسية الكبيرة -->
        <div class="geometric-shapes">
            <div class="main-geometric-shape"></div>
            <div class="secondary-shape"></div>
            <div class="tertiary-shape"></div>
            <div class="quaternary-shape"></div>
        </div>

        <!-- الأشكال المتحركة -->
        <div class="floating-shapes">
            <div class="shape shape-1"></div>
            <div class="shape shape-2"></div>
            <div class="shape shape-3"></div>
            <div class="shape shape-4"></div>
            <div class="shape shape-5"></div>
            <div class="shape shape-6"></div>
            <div class="shape shape-7"></div>
            <div class="shape shape-8"></div>
        </div>

        <!-- خطوط الطاقة -->
        <div class="energy-lines">
            <div class="energy-line line-1"></div>
            <div class="energy-line line-2"></div>
            <div class="energy-line line-3"></div>
        </div>

        <!-- جسيمات متحركة -->
        <div class="particles">
            <div class="particle particle-1"></div>
            <div class="particle particle-2"></div>
            <div class="particle particle-3"></div>
            <div class="particle particle-4"></div>
            <div class="particle particle-5"></div>
        </div>

        <!-- تأثيرات الضوء -->
        <div class="light-effects">
            <div class="light-beam beam-1"></div>
            <div class="light-beam beam-2"></div>
            <div class="light-beam beam-3"></div>
        </div>
    </div>
</body>
</html>
