/* Advanced System Sizing Calculator Styles */

/* Layout */
.system-sizing-layout {
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    gap: 2rem;
    margin-top: 2rem;
}

.system-sizing-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    height: fit-content;
}

.input-section {
    position: sticky;
    top: 2rem;
}

.results-section {
    min-height: 600px;
}

/* Card Headers */
.card-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-header h3 {
    margin: 0;
    color: #4ade80;
    font-size: 1.3rem;
}

/* Calculation Mode Tabs */
.system-sizing-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 0.5rem;
    margin-bottom: 2rem;
    gap: 0.5rem;
}

.tab-button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem;
    border: none;
    background: transparent;
    color: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 0.95rem;
}

.tab-button.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-button:hover:not(.active) {
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.9);
}

.tab-icon {
    font-size: 1.1rem;
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Input Groups */
.input-group {
    margin-bottom: 1.5rem;
}

.input-label {
    display: block;
    margin-bottom: 0.5rem;
}

.label-text {
    display: block;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
}

.label-hint {
    display: block;
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.input-field {
    width: 100%;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: white;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.input-field:focus {
    outline: none;
    border-color: #4ade80;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(74, 222, 128, 0.1);
}

.input-field::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

/* Advanced Settings */
.advanced-settings {
    margin-top: 2rem;
}

.advanced-toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.advanced-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.toggle-text {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

.toggle-icon {
    color: #4ade80;
    font-weight: bold;
    transition: transform 0.3s ease;
}

.advanced-toggle.expanded .toggle-icon {
    transform: rotate(180deg);
}

.advanced-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.advanced-content.expanded {
    max-height: 1000px;
    padding-top: 1.5rem;
}

.advanced-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

/* Calculate Button */
.calculate-button {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #4ade80, #22c55e);
    border: none;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 2rem;
    box-shadow: 0 4px 15px rgba(74, 222, 128, 0.3);
}

.calculate-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(74, 222, 128, 0.4);
}

.calculate-button:active {
    transform: translateY(0);
}

.button-icon {
    font-size: 1.2rem;
}

/* Results Section */
.results-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

.empty-state {
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    margin: 0 0 1rem 0;
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.5rem;
}

.empty-state p {
    margin: 0;
    line-height: 1.6;
    max-width: 400px;
}

.results-content {
    animation: fadeInUp 0.5s ease;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.results-header h3 {
    margin: 0;
    color: #4ade80;
    font-size: 1.4rem;
}

.system-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(74, 222, 128, 0.1);
    border: 1px solid rgba(74, 222, 128, 0.3);
    border-radius: 20px;
    font-size: 0.9rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    background: #4ade80;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Summary Cards */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.summary-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.summary-card.primary {
    background: linear-gradient(135deg, rgba(74, 222, 128, 0.2), rgba(34, 197, 94, 0.1));
    border-color: rgba(74, 222, 128, 0.3);
}

.card-icon {
    font-size: 2rem;
    opacity: 0.8;
}

.card-content {
    flex: 1;
}

.card-value {
    font-size: 1.8rem;
    font-weight: bold;
    color: #4ade80;
    margin-bottom: 0.25rem;
}

.card-label {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.card-change {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
}

/* Results Tabs */
.results-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 0.5rem;
    margin-bottom: 2rem;
    gap: 0.25rem;
}

.results-tab-button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: none;
    background: transparent;
    color: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 0.9rem;
}

.results-tab-button.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.results-tab-button:hover:not(.active) {
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.9);
}

/* Tab Panels */
.results-tab-content {
    min-height: 400px;
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.panel-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-section h4 {
    margin: 0 0 1rem 0;
    color: #4ade80;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .system-sizing-layout {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .input-section {
        position: static;
    }
}

@media (max-width: 768px) {
    .system-sizing-card {
        padding: 1.5rem;
    }
    
    .summary-cards {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .results-tabs {
        flex-wrap: wrap;
    }
    
    .results-tab-button {
        font-size: 0.8rem;
        padding: 0.5rem 0.75rem;
    }
    
    .advanced-grid {
        grid-template-columns: 1fr;
    }
}

/* Technical Analysis Styles */
.technical-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.tech-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    text-align: center;
}

.tech-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
}

.tech-value {
    color: #4ade80;
    font-size: 1.2rem;
    font-weight: bold;
}

.mechanical-specs {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.spec-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

.spec-icon {
    font-size: 1.5rem;
    opacity: 0.8;
}

.spec-content {
    flex: 1;
}

.spec-label {
    display: block;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.spec-value {
    color: #4ade80;
    font-weight: bold;
}

/* Financial Analysis Styles */
.financial-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.financial-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.financial-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.financial-card.cost {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 38, 0.1));
    border-color: rgba(239, 68, 68, 0.3);
}

.financial-card.savings {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(22, 163, 74, 0.1));
    border-color: rgba(34, 197, 94, 0.3);
}

.financial-card.payback {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(37, 99, 235, 0.1));
    border-color: rgba(59, 130, 246, 0.3);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.card-title {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    font-size: 0.9rem;
}

.card-subtitle {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
}

.roi-analysis {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.roi-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

.roi-label {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

.roi-value {
    color: #4ade80;
    font-weight: bold;
    font-size: 1.1rem;
}

.roi-unit {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
    margin-left: 0.25rem;
}

/* Production Analysis Styles */
.production-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.production-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.production-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.production-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.production-value {
    font-size: 2rem;
    font-weight: bold;
    color: #4ade80;
    margin-bottom: 0.5rem;
}

.production-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.production-subtitle {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
}

.performance-metrics {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.metric-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.metric-label {
    min-width: 150px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

.metric-bar {
    flex: 1;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.metric-fill {
    height: 100%;
    background: linear-gradient(90deg, #4ade80, #22c55e);
    border-radius: 4px;
    transition: width 0.5s ease;
}

.metric-value {
    min-width: 60px;
    text-align: right;
    color: #4ade80;
    font-weight: bold;
}

/* Recommendations Styles */
.recommendations-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.recommendation-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: rgba(74, 222, 128, 0.1);
    border: 1px solid rgba(74, 222, 128, 0.3);
    border-radius: 8px;
}

.recommendation-icon {
    font-size: 1.2rem;
    color: #4ade80;
    margin-top: 0.1rem;
}

.recommendation-content {
    flex: 1;
}

.recommendation-title {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.recommendation-text {
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.5;
}

.alerts-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.alert-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 8px;
}

.alert-icon {
    font-size: 1.2rem;
    color: #ef4444;
    margin-top: 0.1rem;
}

.alert-content {
    flex: 1;
}

.alert-title {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.alert-text {
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.5;
}

/* Enhanced Recommendations Styles */
.recommendation-item.high {
    border-left: 4px solid #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

.recommendation-item.medium {
    border-left: 4px solid #f59e0b;
    background: rgba(245, 158, 11, 0.1);
}

.recommendation-item.low {
    border-left: 4px solid #10b981;
    background: rgba(16, 185, 129, 0.1);
}

.recommendation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.recommendation-priority {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-weight: 500;
}

.recommendation-item.high .recommendation-priority {
    background: rgba(239, 68, 68, 0.2);
    color: #fca5a5;
}

.recommendation-item.medium .recommendation-priority {
    background: rgba(245, 158, 11, 0.2);
    color: #fcd34d;
}

.recommendation-item.low .recommendation-priority {
    background: rgba(16, 185, 129, 0.2);
    color: #6ee7b7;
}

.recommendation-impact {
    margin-top: 0.5rem;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    font-size: 0.85rem;
    color: #4ade80;
    font-weight: 500;
}

/* System Comparison Styles */
.comparison-table {
    display: grid;
    grid-template-columns: 2fr 1fr 1.5fr 1fr 1fr;
    gap: 1px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    overflow: hidden;
    margin-top: 1rem;
}

.comparison-header {
    display: contents;
}

.comparison-row {
    display: contents;
}

.comparison-row.current .comparison-cell {
    background: rgba(74, 222, 128, 0.1);
    border: 1px solid rgba(74, 222, 128, 0.3);
}

.comparison-cell {
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
    text-align: center;
}

.comparison-header .comparison-cell {
    background: rgba(255, 255, 255, 0.1);
    font-weight: 600;
    color: #4ade80;
}

/* Sensitivity Analysis Styles */
.sensitivity-summary {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.sensitivity-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

.sensitivity-label {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

.sensitivity-value {
    font-weight: bold;
    font-size: 1.1rem;
}

.sensitivity-value.best {
    color: #22c55e;
}

.sensitivity-value.worst {
    color: #ef4444;
}

.sensitivity-details {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.sensitivity-scenario {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
}

.scenario-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

.scenario-value {
    color: #4ade80;
    font-weight: 500;
}

@media (max-width: 480px) {
    .summary-cards {
        grid-template-columns: 1fr;
    }

    .system-sizing-tabs {
        flex-direction: column;
        gap: 0.5rem;
    }

    .tab-button {
        padding: 0.75rem;
    }

    .financial-summary {
        grid-template-columns: 1fr;
    }

    .production-cards {
        grid-template-columns: 1fr;
    }

    .technical-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .roi-analysis {
        grid-template-columns: 1fr;
    }

    .comparison-table {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .comparison-cell {
        padding: 0.5rem;
        border-radius: 4px;
        margin-bottom: 0.25rem;
    }

    .sensitivity-summary {
        grid-template-columns: 1fr;
    }
}

/* Animation Enhancements */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.summary-card {
    animation: slideInUp 0.5s ease forwards;
}

.summary-card:nth-child(1) { animation-delay: 0.1s; }
.summary-card:nth-child(2) { animation-delay: 0.2s; }
.summary-card:nth-child(3) { animation-delay: 0.3s; }
.summary-card:nth-child(4) { animation-delay: 0.4s; }

.recommendation-item {
    animation: slideInRight 0.5s ease forwards;
}

.recommendation-item:nth-child(1) { animation-delay: 0.1s; }
.recommendation-item:nth-child(2) { animation-delay: 0.2s; }
.recommendation-item:nth-child(3) { animation-delay: 0.3s; }

/* Hover Effects */
.summary-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.recommendation-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.financial-card:hover {
    transform: scale(1.02);
}

.production-card:hover {
    transform: scale(1.02);
}

/* Focus States for Accessibility */
.input-field:focus,
.calculate-button:focus,
.tab-button:focus,
.results-tab-button:focus {
    outline: 2px solid #4ade80;
    outline-offset: 2px;
}

/* Loading Animation for Calculate Button */
.calculate-button.loading::after {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 0.5rem;
}

/* Input Validation Styles */
.input-field.invalid {
    border-color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.input-field.valid {
    border-color: #22c55e;
    background: rgba(34, 197, 94, 0.1);
}

/* Loading States */
.calculate-button.loading {
    opacity: 0.7;
    cursor: not-allowed;
    pointer-events: none;
}

.calculate-button.loading .button-icon {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-left {
    text-align: left;
}

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .system-sizing-card {
        border-width: 2px;
        border-color: white;
    }

    .input-field {
        border-width: 2px;
    }

    .calculate-button {
        border: 2px solid white;
    }
}

/* Print Styles */
@media print {
    .system-sizing-layout {
        grid-template-columns: 1fr;
    }

    .input-section {
        display: none;
    }

    .results-section {
        width: 100%;
        box-shadow: none;
        border: 1px solid #000;
    }

    .results-tabs {
        display: none;
    }

    .tab-panel {
        display: block !important;
        page-break-inside: avoid;
        margin-bottom: 2rem;
    }
}
