/* Advanced System Sizing Calculator Styles */

/* Layout */
.system-sizing-layout {
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    gap: 2rem;
    margin-top: 2rem;
}

.system-sizing-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    height: fit-content;
}

.input-section {
    position: sticky;
    top: 2rem;
}

.results-section {
    min-height: 600px;
}

/* Card Headers */
.card-header {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.card-header h3 {
    margin: 0;
    color: #4ade80;
    font-size: 1.3rem;
}

/* Calculation Mode Tabs */
.system-sizing-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 0.5rem;
    margin-bottom: 2rem;
    gap: 0.5rem;
}

.tab-button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem;
    border: none;
    background: transparent;
    color: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 0.95rem;
}

.tab-button.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-button:hover:not(.active) {
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.9);
}

.tab-icon {
    font-size: 1.1rem;
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Input Groups */
.input-group {
    margin-bottom: 1.5rem;
}

.input-label {
    display: block;
    margin-bottom: 0.5rem;
}

.label-text {
    display: block;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    margin-bottom: 0.25rem;
    font-size: 0.95rem;
}

.label-hint {
    display: block;
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.input-field {
    width: 100%;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: white;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.input-field:focus {
    outline: none;
    border-color: #4ade80;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(74, 222, 128, 0.1);
}

.input-field::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

/* Advanced Settings */
.advanced-settings {
    margin-top: 2rem;
}

.advanced-toggle {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.advanced-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.toggle-text {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

.toggle-icon {
    color: #4ade80;
    font-weight: bold;
    transition: transform 0.3s ease;
}

.advanced-toggle.expanded .toggle-icon {
    transform: rotate(180deg);
}

.advanced-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.advanced-content.expanded {
    max-height: 1000px;
    padding-top: 1.5rem;
}

.advanced-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

/* Calculate Button */
.calculate-button {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #4ade80, #22c55e);
    border: none;
    border-radius: 12px;
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 2rem;
    box-shadow: 0 4px 15px rgba(74, 222, 128, 0.3);
}

.calculate-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(74, 222, 128, 0.4);
}

.calculate-button:active {
    transform: translateY(0);
}

.button-icon {
    font-size: 1.2rem;
}

/* Results Section */
.results-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400px;
}

.empty-state {
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
}

.empty-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h3 {
    margin: 0 0 1rem 0;
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.5rem;
}

.empty-state p {
    margin: 0;
    line-height: 1.6;
    max-width: 400px;
}

.results-content {
    animation: fadeInUp 0.5s ease;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.results-header h3 {
    margin: 0;
    color: #4ade80;
    font-size: 1.4rem;
}

.system-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(74, 222, 128, 0.1);
    border: 1px solid rgba(74, 222, 128, 0.3);
    border-radius: 20px;
    font-size: 0.9rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    background: #4ade80;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Summary Cards */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.summary-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.summary-card.primary {
    background: linear-gradient(135deg, rgba(74, 222, 128, 0.2), rgba(34, 197, 94, 0.1));
    border-color: rgba(74, 222, 128, 0.3);
}

.card-icon {
    font-size: 2rem;
    opacity: 0.8;
}

.card-content {
    flex: 1;
}

.card-value {
    font-size: 1.8rem;
    font-weight: bold;
    color: #4ade80;
    margin-bottom: 0.25rem;
}

.card-label {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.card-change {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
}

/* Results Tabs */
.results-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 0.5rem;
    margin-bottom: 2rem;
    gap: 0.25rem;
}

.results-tab-button {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border: none;
    background: transparent;
    color: rgba(255, 255, 255, 0.7);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    font-size: 0.9rem;
}

.results-tab-button.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.results-tab-button:hover:not(.active) {
    background: rgba(255, 255, 255, 0.05);
    color: rgba(255, 255, 255, 0.9);
}

/* Tab Panels */
.results-tab-content {
    min-height: 400px;
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.panel-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.panel-section h4 {
    margin: 0 0 1rem 0;
    color: #4ade80;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .system-sizing-layout {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .input-section {
        position: static;
    }
}

@media (max-width: 768px) {
    .system-sizing-card {
        padding: 1.5rem;
    }
    
    .summary-cards {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .results-tabs {
        flex-wrap: wrap;
    }
    
    .results-tab-button {
        font-size: 0.8rem;
        padding: 0.5rem 0.75rem;
    }
    
    .advanced-grid {
        grid-template-columns: 1fr;
    }
}

/* Technical Analysis Styles */
.technical-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
}

.tech-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    text-align: center;
}

.tech-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.8rem;
    margin-bottom: 0.5rem;
}

.tech-value {
    color: #4ade80;
    font-size: 1.2rem;
    font-weight: bold;
}

.mechanical-specs {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.spec-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

.spec-icon {
    font-size: 1.5rem;
    opacity: 0.8;
}

.spec-content {
    flex: 1;
}

.spec-label {
    display: block;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.spec-value {
    color: #4ade80;
    font-weight: bold;
}

/* Financial Analysis Styles */
.financial-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.financial-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.financial-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.financial-card.cost {
    background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(220, 38, 38, 0.1));
    border-color: rgba(239, 68, 68, 0.3);
}

.financial-card.savings {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.2), rgba(22, 163, 74, 0.1));
    border-color: rgba(34, 197, 94, 0.3);
}

.financial-card.payback {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(37, 99, 235, 0.1));
    border-color: rgba(59, 130, 246, 0.3);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.card-title {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    font-size: 0.9rem;
}

.card-subtitle {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
}

.roi-analysis {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.roi-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
}

.roi-label {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

.roi-value {
    color: #4ade80;
    font-weight: bold;
    font-size: 1.1rem;
}

.roi-unit {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
    margin-left: 0.25rem;
}

/* Production Analysis Styles */
.production-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.production-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.production-card:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.production-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    opacity: 0.8;
}

.production-value {
    font-size: 2rem;
    font-weight: bold;
    color: #4ade80;
    margin-bottom: 0.5rem;
}

.production-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.production-subtitle {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
}

.performance-metrics {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.metric-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.metric-label {
    min-width: 150px;
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
}

.metric-bar {
    flex: 1;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
}

.metric-fill {
    height: 100%;
    background: linear-gradient(90deg, #4ade80, #22c55e);
    border-radius: 4px;
    transition: width 0.5s ease;
}

.metric-value {
    min-width: 60px;
    text-align: right;
    color: #4ade80;
    font-weight: bold;
}

/* Recommendations Styles */
.recommendations-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.recommendation-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: rgba(74, 222, 128, 0.1);
    border: 1px solid rgba(74, 222, 128, 0.3);
    border-radius: 8px;
}

.recommendation-icon {
    font-size: 1.2rem;
    color: #4ade80;
    margin-top: 0.1rem;
}

.recommendation-content {
    flex: 1;
}

.recommendation-title {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.recommendation-text {
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.5;
}

.alerts-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.alert-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid rgba(239, 68, 68, 0.3);
    border-radius: 8px;
}

.alert-icon {
    font-size: 1.2rem;
    color: #ef4444;
    margin-top: 0.1rem;
}

.alert-content {
    flex: 1;
}

.alert-title {
    color: rgba(255, 255, 255, 0.9);
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.alert-text {
    color: rgba(255, 255, 255, 0.7);
    line-height: 1.5;
}

@media (max-width: 480px) {
    .summary-cards {
        grid-template-columns: 1fr;
    }

    .system-sizing-tabs {
        flex-direction: column;
        gap: 0.5rem;
    }

    .tab-button {
        padding: 0.75rem;
    }

    .financial-summary {
        grid-template-columns: 1fr;
    }

    .production-cards {
        grid-template-columns: 1fr;
    }

    .technical-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .roi-analysis {
        grid-template-columns: 1fr;
    }
}
